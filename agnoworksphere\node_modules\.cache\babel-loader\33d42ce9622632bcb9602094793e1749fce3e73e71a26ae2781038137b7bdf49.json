{"ast": null, "code": "import React,{useState,useEffect}from'react';import Button from'../ui/Button';import Input from'../ui/Input';import Select from'../ui/Select';import Icon from'../AppIcon';import ProjectExportButton from'../ui/ProjectExportButton';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const EnhancedCreateAIProjectModal=_ref=>{let{isOpen,onClose,onCreateAIProject,organizationId,organizationName}=_ref;const[formData,setFormData]=useState({name:'',projectType:'general',teamSize:5,teamExperience:'intermediate'});const[isLoading,setIsLoading]=useState(false);const[error,setError]=useState('');const[currentStep,setCurrentStep]=useState(1);const[projectPreview,setProjectPreview]=useState(null);const[isGeneratingPreview,setIsGeneratingPreview]=useState(false);const projectTypes=[{value:'general',label:'General Project',description:'Standard project with flexible workflow',icon:'Folder',color:'bg-gray-500'},{value:'web_application',label:'Web Application',description:'Full-stack web development project',icon:'Globe',color:'bg-blue-500'},{value:'mobile_app',label:'Mobile App',description:'iOS/Android mobile application',icon:'Smartphone',color:'bg-green-500'},{value:'ecommerce_platform',label:'E-commerce Platform',description:'Online marketplace with payment processing',icon:'ShoppingCart',color:'bg-purple-500'},{value:'saas_application',label:'SaaS Application',description:'Multi-tenant cloud-based software',icon:'Cloud',color:'bg-indigo-500'},{value:'devops_infrastructure',label:'DevOps/Infrastructure',description:'CI/CD pipelines and infrastructure automation',icon:'Server',color:'bg-orange-500'}];const teamSizeOptions=[{value:2,label:'2 people (Small team)'},{value:3,label:'3 people (Small team)'},{value:5,label:'5 people (Optimal team)'},{value:8,label:'8 people (Large team)'},{value:12,label:'12+ people (Enterprise team)'}];const experienceOptions=[{value:'junior',label:'Junior (0-2 years)'},{value:'intermediate',label:'Intermediate (2-5 years)'},{value:'senior',label:'Senior (5+ years)'},{value:'expert',label:'Expert (10+ years)'}];const handleInputChange=(field,value)=>{setFormData(prev=>({...prev,[field]:value}));if(error)setError('');};const generatePreview=async()=>{if(!formData.name.trim()){setError('Project name is required for preview');return;}setIsGeneratingPreview(true);try{await new Promise(resolve=>setTimeout(resolve,2000));const selectedType=projectTypes.find(type=>type.value===formData.projectType);setProjectPreview({name:formData.name,type:selectedType,estimatedDuration:getEstimatedDuration(formData.projectType,formData.teamSize,formData.teamExperience),estimatedTasks:getEstimatedTaskCount(formData.projectType),phases:getProjectPhases(formData.projectType),teamRecommendations:getTeamRecommendations(formData.teamSize,formData.teamExperience),technologies:getTechnologies(formData.projectType)});setCurrentStep(2);}catch(err){setError('Failed to generate preview');}finally{setIsGeneratingPreview(false);}};const getEstimatedDuration=(type,teamSize,experience)=>{const baseDurations={'web_application':50,'mobile_app':57,'ecommerce_platform':87,'saas_application':97,'devops_infrastructure':52,'general':43};let duration=baseDurations[type]||43;const experienceMultipliers={junior:1.3,intermediate:1.0,senior:0.8,expert:0.7};duration*=experienceMultipliers[experience]||1.0;if(teamSize<=2)duration*=1.2;else if(teamSize>=8)duration*=0.9;return Math.round(duration);};const getEstimatedTaskCount=type=>{const taskCounts={'web_application':25,'mobile_app':28,'ecommerce_platform':45,'saas_application':52,'devops_infrastructure':22,'general':15};return taskCounts[type]||15;};const getProjectPhases=type=>{const phases={'web_application':['Planning & Analysis','Design & Architecture','Development','Testing & QA','Deployment & Launch'],'ecommerce_platform':['Market Research & Planning','Core Platform Development','Payment & Security Integration','Advanced Features & Optimization','Testing & Launch']};return phases[type]||['Initiation & Planning','Execution Phase 1','Execution Phase 2','Finalization & Review','Closure & Handover'];};const getTeamRecommendations=(size,experience)=>{const recommendations=[];if(size<=2)recommendations.push('Consider adding more team members for complex phases');if(size>=8)recommendations.push('Break into smaller sub-teams to reduce coordination overhead');if(experience==='junior')recommendations.push('Assign senior mentor for guidance');if(experience==='expert')recommendations.push('Leverage team expertise for innovation opportunities');return recommendations;};const getTechnologies=type=>{const tech={'web_application':['React/Vue.js','Node.js/Python','PostgreSQL','Docker','AWS/Azure'],'ecommerce_platform':['React/Next.js','Node.js/Express','PostgreSQL','Stripe/PayPal','Redis','Docker']};return tech[type]||['Modern Framework','Backend Technology','Database','Cloud Platform'];};const handleSubmit=async e=>{e.preventDefault();if(currentStep===1){generatePreview();return;}setIsLoading(true);setError('');try{const projectData={name:formData.name.trim(),project_type:formData.projectType,team_size:formData.teamSize,team_experience:formData.teamExperience,organization_id:organizationId};await onCreateAIProject(projectData);handleClose();}catch(err){setError(err.message||'Failed to create AI project');}finally{setIsLoading(false);}};const handleClose=()=>{if(!isLoading&&!isGeneratingPreview){setFormData({name:'',projectType:'general',teamSize:5,teamExperience:'intermediate'});setError('');setCurrentStep(1);setProjectPreview(null);onClose();}};const handleBack=()=>{setCurrentStep(1);setProjectPreview(null);setError('');};if(!isOpen)return null;return/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"bg-surface rounded-2xl shadow-enterprise border border-border w-full max-w-6xl max-h-[90vh] overflow-hidden\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between p-8 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg\",children:/*#__PURE__*/_jsx(Icon,{name:\"Sparkles\",size:24,className:\"text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-foreground\",children:\"Create AI Project\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-muted-foreground\",children:\"Let AI generate your complete project structure\"})]})]}),/*#__PURE__*/_jsx(Button,{variant:\"ghost\",size:\"icon\",onClick:handleClose,disabled:isLoading||isGeneratingPreview,className:\"text-muted-foreground hover:text-foreground hover:bg-background/80 rounded-xl\",children:/*#__PURE__*/_jsx(Icon,{name:\"X\",size:20})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"px-8 py-6 bg-background/50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-center space-x-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:`flex items-center transition-all duration-300 ${currentStep>=1?'text-primary':'text-muted-foreground'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${currentStep>=1?'bg-primary text-primary-foreground shadow-lg scale-110':'bg-muted text-muted-foreground'}`,children:\"1\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-3 text-sm font-medium\",children:\"Configure\"})]}),/*#__PURE__*/_jsx(\"div\",{className:`w-20 h-1 rounded-full transition-all duration-300 ${currentStep>=2?'bg-primary':'bg-muted'}`}),/*#__PURE__*/_jsxs(\"div\",{className:`flex items-center transition-all duration-300 ${currentStep>=2?'text-primary':'text-muted-foreground'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${currentStep>=2?'bg-primary text-primary-foreground shadow-lg scale-110':'bg-muted text-muted-foreground'}`,children:\"2\"}),/*#__PURE__*/_jsx(\"span\",{className:\"ml-3 text-sm font-medium\",children:\"Preview & Create\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"p-8 overflow-y-auto max-h-[calc(90vh-200px)]\",children:currentStep===1?/*#__PURE__*/_jsx(ConfigurationStep,{formData:formData,projectTypes:projectTypes,teamSizeOptions:teamSizeOptions,experienceOptions:experienceOptions,onInputChange:handleInputChange,onSubmit:handleSubmit,isGeneratingPreview:isGeneratingPreview,error:error,organizationName:organizationName}):/*#__PURE__*/_jsx(PreviewStep,{projectPreview:projectPreview,onSubmit:handleSubmit,onBack:handleBack,isLoading:isLoading,error:error})})]})});};// Configuration Step Component\nconst ConfigurationStep=_ref2=>{let{formData,projectTypes,teamSizeOptions,experienceOptions,onInputChange,onSubmit,isGeneratingPreview,error,organizationName}=_ref2;return/*#__PURE__*/_jsxs(\"form\",{onSubmit:onSubmit,className:\"space-y-8\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Building2\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-muted-foreground\",children:\"Creating in\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-foreground\",children:organizationName})]})]})}),error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-destructive/10 border border-destructive/20 rounded-xl p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-destructive\",children:[/*#__PURE__*/_jsx(Icon,{name:\"AlertCircle\",size:20}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:error})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-semibold text-foreground\",children:\"Project Name *\"}),/*#__PURE__*/_jsx(Input,{type:\"text\",value:formData.name,onChange:e=>onInputChange('name',e.target.value),placeholder:\"Enter your project name...\",disabled:isGeneratingPreview,className:\"w-full h-12 text-base\",required:true})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-card border border-border rounded-xl p-6 space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Users\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-foreground\",children:\"Team Configuration\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-foreground\",children:\"Team Size\"}),/*#__PURE__*/_jsx(Select,{value:formData.teamSize,onChange:value=>onInputChange('teamSize',value),options:teamSizeOptions,disabled:isGeneratingPreview,className:\"w-full h-12\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-sm font-medium text-foreground\",children:\"Team Experience Level\"}),/*#__PURE__*/_jsx(Select,{value:formData.teamExperience,onChange:value=>onInputChange('teamExperience',value),options:experienceOptions,disabled:isGeneratingPreview,className:\"w-full h-12\"})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Layers\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-foreground\",children:\"Project Type\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-4 max-h-96 overflow-y-auto pr-2\",children:projectTypes.map(type=>/*#__PURE__*/_jsx(\"div\",{className:`group p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${formData.projectType===type.value?'border-primary bg-primary/5 shadow-md scale-[1.02]':'border-border hover:border-primary/50 hover:bg-primary/5'}`,onClick:()=>onInputChange('projectType',type.value),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:`w-12 h-12 ${type.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`,children:/*#__PURE__*/_jsx(Icon,{name:type.icon,size:24,className:\"text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-foreground text-base\",children:type.label}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-muted-foreground mt-1 leading-relaxed\",children:type.description})]}),formData.projectType===type.value&&/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0\",children:/*#__PURE__*/_jsx(Icon,{name:\"Check\",size:14,className:\"text-primary-foreground\"})})]})},type.value))})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-end gap-4 pt-8 border-t border-border\",children:/*#__PURE__*/_jsx(Button,{type:\"submit\",size:\"lg\",disabled:isGeneratingPreview||!formData.name.trim(),iconName:isGeneratingPreview?\"Loader2\":\"Sparkles\",iconPosition:\"left\",className:`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${isGeneratingPreview?\"animate-pulse\":\"\"}`,children:isGeneratingPreview?'Generating Preview...':'Generate AI Preview'})})]});};// Preview Step Component\nconst PreviewStep=_ref3=>{var _projectPreview$type,_projectPreview$type2,_projectPreview$type3,_projectPreview$phase,_projectPreview$techn,_projectPreview$phase2,_projectPreview$techn2,_projectPreview$teamR;let{projectPreview,onSubmit,onBack,isLoading,error}=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-8\",children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"bg-destructive/10 border border-destructive/20 rounded-xl p-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-destructive\",children:[/*#__PURE__*/_jsx(Icon,{name:\"AlertCircle\",size:20}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium\",children:error})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 border border-purple-200 rounded-2xl p-8 shadow-lg\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:`w-16 h-16 ${(projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$type=projectPreview.type)===null||_projectPreview$type===void 0?void 0:_projectPreview$type.color)||'bg-gray-500'} rounded-2xl flex items-center justify-center shadow-lg`,children:/*#__PURE__*/_jsx(Icon,{name:(projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$type2=projectPreview.type)===null||_projectPreview$type2===void 0?void 0:_projectPreview$type2.icon)||'Folder',size:32,className:\"text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-foreground\",children:projectPreview===null||projectPreview===void 0?void 0:projectPreview.name}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-muted-foreground\",children:projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$type3=projectPreview.type)===null||_projectPreview$type3===void 0?void 0:_projectPreview$type3.label})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 md:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center bg-white/60 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-primary\",children:projectPreview===null||projectPreview===void 0?void 0:projectPreview.estimatedDuration}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-muted-foreground\",children:\"Days\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center bg-white/60 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-primary\",children:projectPreview===null||projectPreview===void 0?void 0:projectPreview.estimatedTasks}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-muted-foreground\",children:\"Tasks\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center bg-white/60 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-primary\",children:(projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$phase=projectPreview.phases)===null||_projectPreview$phase===void 0?void 0:_projectPreview$phase.length)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-muted-foreground\",children:\"Phases\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center bg-white/60 rounded-xl p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-primary\",children:(projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$techn=projectPreview.technologies)===null||_projectPreview$techn===void 0?void 0:_projectPreview$techn.length)||0}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-muted-foreground\",children:\"Technologies\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 lg:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-card border border-border rounded-xl p-6 space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"GitBranch\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-foreground\",children:\"Project Phases\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$phase2=projectPreview.phases)===null||_projectPreview$phase2===void 0?void 0:_projectPreview$phase2.map((phase,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 p-4 bg-background rounded-lg border border-border hover:shadow-sm transition-shadow\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-sm\",children:index+1}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-foreground\",children:phase})]},index))})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-card border border-border rounded-xl p-6 space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Code\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-foreground\",children:\"Recommended Technologies\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3\",children:projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$techn2=projectPreview.technologies)===null||_projectPreview$techn2===void 0?void 0:_projectPreview$techn2.map((tech,index)=>/*#__PURE__*/_jsx(\"span\",{className:\"px-4 py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium border border-primary/20 hover:bg-primary/20 transition-colors\",children:tech},index))}),(projectPreview===null||projectPreview===void 0?void 0:(_projectPreview$teamR=projectPreview.teamRecommendations)===null||_projectPreview$teamR===void 0?void 0:_projectPreview$teamR.length)>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"pt-4 border-t border-border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-6 h-6 bg-orange-500 rounded-md flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Users\",size:14,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h4\",{className:\"font-semibold text-foreground\",children:\"Team Recommendations\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:projectPreview.teamRecommendations.map((rec,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start gap-3 text-sm text-muted-foreground\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-green-500 mt-0.5 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:rec})]},index))})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 border border-emerald-200 rounded-xl p-6 shadow-sm\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 mb-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(Icon,{name:\"Sparkles\",size:16,className:\"text-white\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-emerald-900\",children:\"What AI will generate for you:\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Comprehensive project description and objectives\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Detailed workflow with phases and milestones\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Complete task breakdown with priorities and estimates\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Kanban board with organized task categories\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Project timeline and resource recommendations\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3 text-sm text-emerald-800\",children:[/*#__PURE__*/_jsx(Icon,{name:\"CheckCircle\",size:16,className:\"text-emerald-600 flex-shrink-0\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Risk assessment and mitigation strategies\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between pt-8 border-t border-border\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsx(Button,{variant:\"outline\",size:\"lg\",onClick:onBack,disabled:isLoading,iconName:\"ArrowLeft\",iconPosition:\"left\",className:\"border-2\",children:\"Back to Configuration\"}),/*#__PURE__*/_jsx(ProjectExportButton,{projectData:projectPreview})]}),/*#__PURE__*/_jsx(Button,{size:\"lg\",onClick:onSubmit,disabled:isLoading,iconName:isLoading?\"Loader2\":\"Rocket\",iconPosition:\"left\",className:`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${isLoading?\"animate-pulse\":\"\"}`,children:isLoading?'Creating Project...':'Create AI Project'})]})]});};export default EnhancedCreateAIProjectModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Select", "Icon", "ProjectExportButton", "jsx", "_jsx", "jsxs", "_jsxs", "EnhancedCreateAIProjectModal", "_ref", "isOpen", "onClose", "onCreateAIProject", "organizationId", "organizationName", "formData", "setFormData", "name", "projectType", "teamSize", "teamExperience", "isLoading", "setIsLoading", "error", "setError", "currentStep", "setCurrentStep", "projectPreview", "setProjectPreview", "isGeneratingPreview", "setIsGeneratingPreview", "projectTypes", "value", "label", "description", "icon", "color", "teamSizeOptions", "experienceOptions", "handleInputChange", "field", "prev", "generatePreview", "trim", "Promise", "resolve", "setTimeout", "selectedType", "find", "type", "estimatedDuration", "getEstimatedDuration", "estimatedTasks", "getEstimatedTaskCount", "phases", "getProjectPhases", "teamRecommendations", "getTeamRecommendations", "technologies", "getTechnologies", "err", "experience", "baseDurations", "duration", "experienceMultipliers", "junior", "intermediate", "senior", "expert", "Math", "round", "taskCounts", "size", "recommendations", "push", "tech", "handleSubmit", "e", "preventDefault", "projectData", "project_type", "team_size", "team_experience", "organization_id", "handleClose", "message", "handleBack", "className", "children", "variant", "onClick", "disabled", "ConfigurationStep", "onInputChange", "onSubmit", "PreviewStep", "onBack", "_ref2", "onChange", "target", "placeholder", "required", "options", "map", "iconName", "iconPosition", "_ref3", "_projectPreview$type", "_projectPreview$type2", "_projectPreview$type3", "_projectPreview$phase", "_projectPreview$techn", "_projectPreview$phase2", "_projectPreview$techn2", "_projectPreview$teamR", "length", "phase", "index", "rec"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/modals/EnhancedCreateAIProjectModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Icon from '../AppIcon';\nimport ProjectExportButton from '../ui/ProjectExportButton';\n\nconst EnhancedCreateAIProjectModal = ({ isOpen, onClose, onCreateAIProject, organizationId, organizationName }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    projectType: 'general',\n    teamSize: 5,\n    teamExperience: 'intermediate'\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState(1);\n  const [projectPreview, setProjectPreview] = useState(null);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n\n  const projectTypes = [\n    { value: 'general', label: 'General Project', description: 'Standard project with flexible workflow', icon: 'Folder', color: 'bg-gray-500' },\n    { value: 'web_application', label: 'Web Application', description: 'Full-stack web development project', icon: 'Globe', color: 'bg-blue-500' },\n    { value: 'mobile_app', label: 'Mobile App', description: 'iOS/Android mobile application', icon: 'Smartphone', color: 'bg-green-500' },\n    { value: 'ecommerce_platform', label: 'E-commerce Platform', description: 'Online marketplace with payment processing', icon: 'ShoppingCart', color: 'bg-purple-500' },\n    { value: 'saas_application', label: 'SaaS Application', description: 'Multi-tenant cloud-based software', icon: 'Cloud', color: 'bg-indigo-500' },\n    { value: 'devops_infrastructure', label: 'DevOps/Infrastructure', description: 'CI/CD pipelines and infrastructure automation', icon: 'Server', color: 'bg-orange-500' }\n  ];\n\n  const teamSizeOptions = [\n    { value: 2, label: '2 people (Small team)' },\n    { value: 3, label: '3 people (Small team)' },\n    { value: 5, label: '5 people (Optimal team)' },\n    { value: 8, label: '8 people (Large team)' },\n    { value: 12, label: '12+ people (Enterprise team)' }\n  ];\n\n  const experienceOptions = [\n    { value: 'junior', label: 'Junior (0-2 years)' },\n    { value: 'intermediate', label: 'Intermediate (2-5 years)' },\n    { value: 'senior', label: 'Senior (5+ years)' },\n    { value: 'expert', label: 'Expert (10+ years)' }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (error) setError('');\n  };\n\n  const generatePreview = async () => {\n    if (!formData.name.trim()) {\n      setError('Project name is required for preview');\n      return;\n    }\n\n    setIsGeneratingPreview(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const selectedType = projectTypes.find(type => type.value === formData.projectType);\n      \n      setProjectPreview({\n        name: formData.name,\n        type: selectedType,\n        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),\n        estimatedTasks: getEstimatedTaskCount(formData.projectType),\n        phases: getProjectPhases(formData.projectType),\n        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),\n        technologies: getTechnologies(formData.projectType)\n      });\n      \n      setCurrentStep(2);\n    } catch (err) {\n      setError('Failed to generate preview');\n    } finally {\n      setIsGeneratingPreview(false);\n    }\n  };\n\n  const getEstimatedDuration = (type, teamSize, experience) => {\n    const baseDurations = {\n      'web_application': 50, 'mobile_app': 57, 'ecommerce_platform': 87,\n      'saas_application': 97, 'devops_infrastructure': 52, 'general': 43\n    };\n    \n    let duration = baseDurations[type] || 43;\n    const experienceMultipliers = { junior: 1.3, intermediate: 1.0, senior: 0.8, expert: 0.7 };\n    duration *= experienceMultipliers[experience] || 1.0;\n    \n    if (teamSize <= 2) duration *= 1.2;\n    else if (teamSize >= 8) duration *= 0.9;\n    \n    return Math.round(duration);\n  };\n\n  const getEstimatedTaskCount = (type) => {\n    const taskCounts = {\n      'web_application': 25, 'mobile_app': 28, 'ecommerce_platform': 45,\n      'saas_application': 52, 'devops_infrastructure': 22, 'general': 15\n    };\n    return taskCounts[type] || 15;\n  };\n\n  const getProjectPhases = (type) => {\n    const phases = {\n      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],\n      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch']\n    };\n    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];\n  };\n\n  const getTeamRecommendations = (size, experience) => {\n    const recommendations = [];\n    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');\n    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');\n    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');\n    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');\n    return recommendations;\n  };\n\n  const getTechnologies = (type) => {\n    const tech = {\n      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],\n      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker']\n    };\n    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (currentStep === 1) {\n      generatePreview();\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        project_type: formData.projectType,\n        team_size: formData.teamSize,\n        team_experience: formData.teamExperience,\n        organization_id: organizationId\n      };\n\n      await onCreateAIProject(projectData);\n      handleClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create AI project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLoading && !isGeneratingPreview) {\n      setFormData({ name: '', projectType: 'general', teamSize: 5, teamExperience: 'intermediate' });\n      setError('');\n      setCurrentStep(1);\n      setProjectPreview(null);\n      onClose();\n    }\n  };\n\n  const handleBack = () => {\n    setCurrentStep(1);\n    setProjectPreview(null);\n    setError('');\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-surface rounded-2xl shadow-enterprise border border-border w-full max-w-6xl max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-8 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg\">\n              <Icon name=\"Sparkles\" size={24} className=\"text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-foreground\">Create AI Project</h2>\n              <p className=\"text-muted-foreground\">Let AI generate your complete project structure</p>\n            </div>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleClose}\n            disabled={isLoading || isGeneratingPreview}\n            className=\"text-muted-foreground hover:text-foreground hover:bg-background/80 rounded-xl\"\n          >\n            <Icon name=\"X\" size={20} />\n          </Button>\n        </div>\n\n        {/* Step Indicator */}\n        <div className=\"px-8 py-6 bg-background/50\">\n          <div className=\"flex items-center justify-center space-x-6\">\n            <div className={`flex items-center transition-all duration-300 ${currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${\n                currentStep >= 1\n                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'\n                  : 'bg-muted text-muted-foreground'\n              }`}>\n                1\n              </div>\n              <span className=\"ml-3 text-sm font-medium\">Configure</span>\n            </div>\n            <div className={`w-20 h-1 rounded-full transition-all duration-300 ${\n              currentStep >= 2 ? 'bg-primary' : 'bg-muted'\n            }`}></div>\n            <div className={`flex items-center transition-all duration-300 ${currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${\n                currentStep >= 2\n                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'\n                  : 'bg-muted text-muted-foreground'\n              }`}>\n                2\n              </div>\n              <span className=\"ml-3 text-sm font-medium\">Preview & Create</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-8 overflow-y-auto max-h-[calc(90vh-200px)]\">\n          {currentStep === 1 ? (\n            <ConfigurationStep\n              formData={formData}\n              projectTypes={projectTypes}\n              teamSizeOptions={teamSizeOptions}\n              experienceOptions={experienceOptions}\n              onInputChange={handleInputChange}\n              onSubmit={handleSubmit}\n              isGeneratingPreview={isGeneratingPreview}\n              error={error}\n              organizationName={organizationName}\n            />\n          ) : (\n            <PreviewStep\n              projectPreview={projectPreview}\n              onSubmit={handleSubmit}\n              onBack={handleBack}\n              isLoading={isLoading}\n              error={error}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Configuration Step Component\nconst ConfigurationStep = ({ formData, projectTypes, teamSizeOptions, experienceOptions, onInputChange, onSubmit, isGeneratingPreview, error, organizationName }) => (\n  <form onSubmit={onSubmit} className=\"space-y-8\">\n    {/* Organization Info */}\n    <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6\">\n      <div className=\"flex items-center gap-3\">\n        <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n          <Icon name=\"Building2\" size={16} className=\"text-white\" />\n        </div>\n        <div>\n          <p className=\"text-sm text-muted-foreground\">Creating in</p>\n          <p className=\"font-semibold text-foreground\">{organizationName}</p>\n        </div>\n      </div>\n    </div>\n\n    {/* Error Message */}\n    {error && (\n      <div className=\"bg-destructive/10 border border-destructive/20 rounded-xl p-4\">\n        <div className=\"flex items-center gap-3 text-destructive\">\n          <Icon name=\"AlertCircle\" size={20} />\n          <span className=\"font-medium\">{error}</span>\n        </div>\n      </div>\n    )}\n\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n      {/* Left Column */}\n      <div className=\"space-y-8\">\n        {/* Project Name */}\n        <div className=\"space-y-3\">\n          <label className=\"block text-sm font-semibold text-foreground\">Project Name *</label>\n          <Input\n            type=\"text\"\n            value={formData.name}\n            onChange={(e) => onInputChange('name', e.target.value)}\n            placeholder=\"Enter your project name...\"\n            disabled={isGeneratingPreview}\n            className=\"w-full h-12 text-base\"\n            required\n          />\n        </div>\n\n        {/* Team Configuration */}\n        <div className=\"bg-card border border-border rounded-xl p-6 space-y-6\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\">\n              <Icon name=\"Users\" size={16} className=\"text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-foreground\">Team Configuration</h3>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm font-medium text-foreground\">Team Size</label>\n              <Select\n                value={formData.teamSize}\n                onChange={(value) => onInputChange('teamSize', value)}\n                options={teamSizeOptions}\n                disabled={isGeneratingPreview}\n                className=\"w-full h-12\"\n              />\n            </div>\n\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm font-medium text-foreground\">Team Experience Level</label>\n              <Select\n                value={formData.teamExperience}\n                onChange={(value) => onInputChange('teamExperience', value)}\n                options={experienceOptions}\n                disabled={isGeneratingPreview}\n                className=\"w-full h-12\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Column - Project Type Selector */}\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center\">\n            <Icon name=\"Layers\" size={16} className=\"text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-foreground\">Project Type</h3>\n        </div>\n        <div className=\"grid grid-cols-1 gap-4 max-h-96 overflow-y-auto pr-2\">\n          {projectTypes.map((type) => (\n            <div\n              key={type.value}\n              className={`group p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${\n                formData.projectType === type.value\n                  ? 'border-primary bg-primary/5 shadow-md scale-[1.02]'\n                  : 'border-border hover:border-primary/50 hover:bg-primary/5'\n              }`}\n              onClick={() => onInputChange('projectType', type.value)}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-12 h-12 ${type.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`}>\n                  <Icon name={type.icon} size={24} className=\"text-white\" />\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"font-semibold text-foreground text-base\">{type.label}</h4>\n                  <p className=\"text-sm text-muted-foreground mt-1 leading-relaxed\">{type.description}</p>\n                </div>\n                {formData.projectType === type.value && (\n                  <div className=\"w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0\">\n                    <Icon name=\"Check\" size={14} className=\"text-primary-foreground\" />\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n\n    {/* Actions */}\n    <div className=\"flex items-center justify-end gap-4 pt-8 border-t border-border\">\n      <Button\n        type=\"submit\"\n        size=\"lg\"\n        disabled={isGeneratingPreview || !formData.name.trim()}\n        iconName={isGeneratingPreview ? \"Loader2\" : \"Sparkles\"}\n        iconPosition=\"left\"\n        className={`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${\n          isGeneratingPreview ? \"animate-pulse\" : \"\"\n        }`}\n      >\n        {isGeneratingPreview ? 'Generating Preview...' : 'Generate AI Preview'}\n      </Button>\n    </div>\n  </form>\n);\n\n// Preview Step Component\nconst PreviewStep = ({ projectPreview, onSubmit, onBack, isLoading, error }) => (\n  <div className=\"space-y-8\">\n    {/* Error Message */}\n    {error && (\n      <div className=\"bg-destructive/10 border border-destructive/20 rounded-xl p-4\">\n        <div className=\"flex items-center gap-3 text-destructive\">\n          <Icon name=\"AlertCircle\" size={20} />\n          <span className=\"font-medium\">{error}</span>\n        </div>\n      </div>\n    )}\n\n    {/* Project Overview */}\n    <div className=\"bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 border border-purple-200 rounded-2xl p-8 shadow-lg\">\n      <div className=\"flex items-center gap-4 mb-6\">\n        <div className={`w-16 h-16 ${projectPreview?.type?.color || 'bg-gray-500'} rounded-2xl flex items-center justify-center shadow-lg`}>\n          <Icon name={projectPreview?.type?.icon || 'Folder'} size={32} className=\"text-white\" />\n        </div>\n        <div>\n          <h2 className=\"text-2xl font-bold text-foreground\">{projectPreview?.name}</h2>\n          <p className=\"text-lg text-muted-foreground\">{projectPreview?.type?.label}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6\">\n        <div className=\"text-center bg-white/60 rounded-xl p-4\">\n          <div className=\"text-3xl font-bold text-primary\">{projectPreview?.estimatedDuration}</div>\n          <div className=\"text-sm font-medium text-muted-foreground\">Days</div>\n        </div>\n        <div className=\"text-center bg-white/60 rounded-xl p-4\">\n          <div className=\"text-3xl font-bold text-primary\">{projectPreview?.estimatedTasks}</div>\n          <div className=\"text-sm font-medium text-muted-foreground\">Tasks</div>\n        </div>\n        <div className=\"text-center bg-white/60 rounded-xl p-4\">\n          <div className=\"text-3xl font-bold text-primary\">{projectPreview?.phases?.length || 0}</div>\n          <div className=\"text-sm font-medium text-muted-foreground\">Phases</div>\n        </div>\n        <div className=\"text-center bg-white/60 rounded-xl p-4\">\n          <div className=\"text-3xl font-bold text-primary\">{projectPreview?.technologies?.length || 0}</div>\n          <div className=\"text-sm font-medium text-muted-foreground\">Technologies</div>\n        </div>\n      </div>\n    </div>\n\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n      {/* Project Phases */}\n      <div className=\"bg-card border border-border rounded-xl p-6 space-y-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n            <Icon name=\"GitBranch\" size={16} className=\"text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-foreground\">Project Phases</h3>\n        </div>\n        <div className=\"space-y-3\">\n          {projectPreview?.phases?.map((phase, index) => (\n            <div key={index} className=\"flex items-center gap-4 p-4 bg-background rounded-lg border border-border hover:shadow-sm transition-shadow\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-sm\">\n                {index + 1}\n              </div>\n              <span className=\"font-medium text-foreground\">{phase}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Technologies */}\n      <div className=\"bg-card border border-border rounded-xl p-6 space-y-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\">\n            <Icon name=\"Code\" size={16} className=\"text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-foreground\">Recommended Technologies</h3>\n        </div>\n        <div className=\"flex flex-wrap gap-3\">\n          {projectPreview?.technologies?.map((tech, index) => (\n            <span key={index} className=\"px-4 py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium border border-primary/20 hover:bg-primary/20 transition-colors\">\n              {tech}\n            </span>\n          ))}\n        </div>\n\n        {/* Team Recommendations */}\n        {projectPreview?.teamRecommendations?.length > 0 && (\n          <div className=\"pt-4 border-t border-border\">\n            <div className=\"flex items-center gap-3 mb-4\">\n              <div className=\"w-6 h-6 bg-orange-500 rounded-md flex items-center justify-center\">\n                <Icon name=\"Users\" size={14} className=\"text-white\" />\n              </div>\n              <h4 className=\"font-semibold text-foreground\">Team Recommendations</h4>\n            </div>\n            <div className=\"space-y-3\">\n              {projectPreview.teamRecommendations.map((rec, index) => (\n                <div key={index} className=\"flex items-start gap-3 text-sm text-muted-foreground\">\n                  <Icon name=\"CheckCircle\" size={16} className=\"text-green-500 mt-0.5 flex-shrink-0\" />\n                  <span>{rec}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n\n    {/* AI Features Info */}\n    <div className=\"bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 border border-emerald-200 rounded-xl p-6 shadow-sm\">\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className=\"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center\">\n          <Icon name=\"Sparkles\" size={16} className=\"text-white\" />\n        </div>\n        <h3 className=\"text-lg font-semibold text-emerald-900\">What AI will generate for you:</h3>\n      </div>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Comprehensive project description and objectives</span>\n        </div>\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Detailed workflow with phases and milestones</span>\n        </div>\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Complete task breakdown with priorities and estimates</span>\n        </div>\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Kanban board with organized task categories</span>\n        </div>\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Project timeline and resource recommendations</span>\n        </div>\n        <div className=\"flex items-center gap-3 text-sm text-emerald-800\">\n          <Icon name=\"CheckCircle\" size={16} className=\"text-emerald-600 flex-shrink-0\" />\n          <span>Risk assessment and mitigation strategies</span>\n        </div>\n      </div>\n    </div>\n\n    <div className=\"flex items-center justify-between pt-8 border-t border-border\">\n      <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"outline\"\n          size=\"lg\"\n          onClick={onBack}\n          disabled={isLoading}\n          iconName=\"ArrowLeft\"\n          iconPosition=\"left\"\n          className=\"border-2\"\n        >\n          Back to Configuration\n        </Button>\n        <ProjectExportButton projectData={projectPreview} />\n      </div>\n      <Button\n        size=\"lg\"\n        onClick={onSubmit}\n        disabled={isLoading}\n        iconName={isLoading ? \"Loader2\" : \"Rocket\"}\n        iconPosition=\"left\"\n        className={`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${\n          isLoading ? \"animate-pulse\" : \"\"\n        }`}\n      >\n        {isLoading ? 'Creating Project...' : 'Create AI Project'}\n      </Button>\n    </div>\n  </div>\n);\n\nexport default EnhancedCreateAIProjectModal;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,KAAK,KAAM,aAAa,CAC/B,MAAO,CAAAC,MAAM,KAAM,cAAc,CACjC,MAAO,CAAAC,IAAI,KAAM,YAAY,CAC7B,MAAO,CAAAC,mBAAmB,KAAM,2BAA2B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE5D,KAAM,CAAAC,4BAA4B,CAAGC,IAAA,EAA8E,IAA7E,CAAEC,MAAM,CAAEC,OAAO,CAAEC,iBAAiB,CAAEC,cAAc,CAAEC,gBAAiB,CAAC,CAAAL,IAAA,CAC5G,KAAM,CAACM,QAAQ,CAAEC,WAAW,CAAC,CAAGnB,QAAQ,CAAC,CACvCoB,IAAI,CAAE,EAAE,CACRC,WAAW,CAAE,SAAS,CACtBC,QAAQ,CAAE,CAAC,CACXC,cAAc,CAAE,cAClB,CAAC,CAAC,CACF,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGzB,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC0B,KAAK,CAAEC,QAAQ,CAAC,CAAG3B,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAAC4B,WAAW,CAAEC,cAAc,CAAC,CAAG7B,QAAQ,CAAC,CAAC,CAAC,CACjD,KAAM,CAAC8B,cAAc,CAAEC,iBAAiB,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACgC,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAErE,KAAM,CAAAkC,YAAY,CAAG,CACnB,CAAEC,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,WAAW,CAAE,yCAAyC,CAAEC,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,aAAc,CAAC,CAC5I,CAAEJ,KAAK,CAAE,iBAAiB,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,WAAW,CAAE,oCAAoC,CAAEC,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,aAAc,CAAC,CAC9I,CAAEJ,KAAK,CAAE,YAAY,CAAEC,KAAK,CAAE,YAAY,CAAEC,WAAW,CAAE,gCAAgC,CAAEC,IAAI,CAAE,YAAY,CAAEC,KAAK,CAAE,cAAe,CAAC,CACtI,CAAEJ,KAAK,CAAE,oBAAoB,CAAEC,KAAK,CAAE,qBAAqB,CAAEC,WAAW,CAAE,4CAA4C,CAAEC,IAAI,CAAE,cAAc,CAAEC,KAAK,CAAE,eAAgB,CAAC,CACtK,CAAEJ,KAAK,CAAE,kBAAkB,CAAEC,KAAK,CAAE,kBAAkB,CAAEC,WAAW,CAAE,mCAAmC,CAAEC,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,eAAgB,CAAC,CACjJ,CAAEJ,KAAK,CAAE,uBAAuB,CAAEC,KAAK,CAAE,uBAAuB,CAAEC,WAAW,CAAE,+CAA+C,CAAEC,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,eAAgB,CAAC,CACzK,CAED,KAAM,CAAAC,eAAe,CAAG,CACtB,CAAEL,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,uBAAwB,CAAC,CAC5C,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,uBAAwB,CAAC,CAC5C,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,yBAA0B,CAAC,CAC9C,CAAED,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,uBAAwB,CAAC,CAC5C,CAAED,KAAK,CAAE,EAAE,CAAEC,KAAK,CAAE,8BAA+B,CAAC,CACrD,CAED,KAAM,CAAAK,iBAAiB,CAAG,CACxB,CAAEN,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CAChD,CAAED,KAAK,CAAE,cAAc,CAAEC,KAAK,CAAE,0BAA2B,CAAC,CAC5D,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,mBAAoB,CAAC,CAC/C,CAAED,KAAK,CAAE,QAAQ,CAAEC,KAAK,CAAE,oBAAqB,CAAC,CACjD,CAED,KAAM,CAAAM,iBAAiB,CAAGA,CAACC,KAAK,CAAER,KAAK,GAAK,CAC1ChB,WAAW,CAACyB,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACD,KAAK,EAAGR,KAAM,CAAC,CAAC,CAAC,CAClD,GAAIT,KAAK,CAAEC,QAAQ,CAAC,EAAE,CAAC,CACzB,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAAC3B,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAAE,CACzBnB,QAAQ,CAAC,sCAAsC,CAAC,CAChD,OACF,CAEAM,sBAAsB,CAAC,IAAI,CAAC,CAC5B,GAAI,CACF,KAAM,IAAI,CAAAc,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD,KAAM,CAAAE,YAAY,CAAGhB,YAAY,CAACiB,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACjB,KAAK,GAAKjB,QAAQ,CAACG,WAAW,CAAC,CAEnFU,iBAAiB,CAAC,CAChBX,IAAI,CAAEF,QAAQ,CAACE,IAAI,CACnBgC,IAAI,CAAEF,YAAY,CAClBG,iBAAiB,CAAEC,oBAAoB,CAACpC,QAAQ,CAACG,WAAW,CAAEH,QAAQ,CAACI,QAAQ,CAAEJ,QAAQ,CAACK,cAAc,CAAC,CACzGgC,cAAc,CAAEC,qBAAqB,CAACtC,QAAQ,CAACG,WAAW,CAAC,CAC3DoC,MAAM,CAAEC,gBAAgB,CAACxC,QAAQ,CAACG,WAAW,CAAC,CAC9CsC,mBAAmB,CAAEC,sBAAsB,CAAC1C,QAAQ,CAACI,QAAQ,CAAEJ,QAAQ,CAACK,cAAc,CAAC,CACvFsC,YAAY,CAAEC,eAAe,CAAC5C,QAAQ,CAACG,WAAW,CACpD,CAAC,CAAC,CAEFQ,cAAc,CAAC,CAAC,CAAC,CACnB,CAAE,MAAOkC,GAAG,CAAE,CACZpC,QAAQ,CAAC,4BAA4B,CAAC,CACxC,CAAC,OAAS,CACRM,sBAAsB,CAAC,KAAK,CAAC,CAC/B,CACF,CAAC,CAED,KAAM,CAAAqB,oBAAoB,CAAGA,CAACF,IAAI,CAAE9B,QAAQ,CAAE0C,UAAU,GAAK,CAC3D,KAAM,CAAAC,aAAa,CAAG,CACpB,iBAAiB,CAAE,EAAE,CAAE,YAAY,CAAE,EAAE,CAAE,oBAAoB,CAAE,EAAE,CACjE,kBAAkB,CAAE,EAAE,CAAE,uBAAuB,CAAE,EAAE,CAAE,SAAS,CAAE,EAClE,CAAC,CAED,GAAI,CAAAC,QAAQ,CAAGD,aAAa,CAACb,IAAI,CAAC,EAAI,EAAE,CACxC,KAAM,CAAAe,qBAAqB,CAAG,CAAEC,MAAM,CAAE,GAAG,CAAEC,YAAY,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,GAAI,CAAC,CAC1FL,QAAQ,EAAIC,qBAAqB,CAACH,UAAU,CAAC,EAAI,GAAG,CAEpD,GAAI1C,QAAQ,EAAI,CAAC,CAAE4C,QAAQ,EAAI,GAAG,CAAC,IAC9B,IAAI5C,QAAQ,EAAI,CAAC,CAAE4C,QAAQ,EAAI,GAAG,CAEvC,MAAO,CAAAM,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAV,qBAAqB,CAAIJ,IAAI,EAAK,CACtC,KAAM,CAAAsB,UAAU,CAAG,CACjB,iBAAiB,CAAE,EAAE,CAAE,YAAY,CAAE,EAAE,CAAE,oBAAoB,CAAE,EAAE,CACjE,kBAAkB,CAAE,EAAE,CAAE,uBAAuB,CAAE,EAAE,CAAE,SAAS,CAAE,EAClE,CAAC,CACD,MAAO,CAAAA,UAAU,CAACtB,IAAI,CAAC,EAAI,EAAE,CAC/B,CAAC,CAED,KAAM,CAAAM,gBAAgB,CAAIN,IAAI,EAAK,CACjC,KAAM,CAAAK,MAAM,CAAG,CACb,iBAAiB,CAAE,CAAC,qBAAqB,CAAE,uBAAuB,CAAE,aAAa,CAAE,cAAc,CAAE,qBAAqB,CAAC,CACzH,oBAAoB,CAAE,CAAC,4BAA4B,CAAE,2BAA2B,CAAE,gCAAgC,CAAE,kCAAkC,CAAE,kBAAkB,CAC5K,CAAC,CACD,MAAO,CAAAA,MAAM,CAACL,IAAI,CAAC,EAAI,CAAC,uBAAuB,CAAE,mBAAmB,CAAE,mBAAmB,CAAE,uBAAuB,CAAE,oBAAoB,CAAC,CAC3I,CAAC,CAED,KAAM,CAAAQ,sBAAsB,CAAGA,CAACe,IAAI,CAAEX,UAAU,GAAK,CACnD,KAAM,CAAAY,eAAe,CAAG,EAAE,CAC1B,GAAID,IAAI,EAAI,CAAC,CAAEC,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC,CAC3F,GAAIF,IAAI,EAAI,CAAC,CAAEC,eAAe,CAACC,IAAI,CAAC,8DAA8D,CAAC,CACnG,GAAIb,UAAU,GAAK,QAAQ,CAAEY,eAAe,CAACC,IAAI,CAAC,mCAAmC,CAAC,CACtF,GAAIb,UAAU,GAAK,QAAQ,CAAEY,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC,CACzG,MAAO,CAAAD,eAAe,CACxB,CAAC,CAED,KAAM,CAAAd,eAAe,CAAIV,IAAI,EAAK,CAChC,KAAM,CAAA0B,IAAI,CAAG,CACX,iBAAiB,CAAE,CAAC,cAAc,CAAE,gBAAgB,CAAE,YAAY,CAAE,QAAQ,CAAE,WAAW,CAAC,CAC1F,oBAAoB,CAAE,CAAC,eAAe,CAAE,iBAAiB,CAAE,YAAY,CAAE,eAAe,CAAE,OAAO,CAAE,QAAQ,CAC7G,CAAC,CACD,MAAO,CAAAA,IAAI,CAAC1B,IAAI,CAAC,EAAI,CAAC,kBAAkB,CAAE,oBAAoB,CAAE,UAAU,CAAE,gBAAgB,CAAC,CAC/F,CAAC,CAED,KAAM,CAAA2B,YAAY,CAAG,KAAO,CAAAC,CAAC,EAAK,CAChCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAElB,GAAIrD,WAAW,GAAK,CAAC,CAAE,CACrBiB,eAAe,CAAC,CAAC,CACjB,OACF,CAEApB,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,EAAE,CAAC,CAEZ,GAAI,CACF,KAAM,CAAAuD,WAAW,CAAG,CAClB9D,IAAI,CAAEF,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,CAC1BqC,YAAY,CAAEjE,QAAQ,CAACG,WAAW,CAClC+D,SAAS,CAAElE,QAAQ,CAACI,QAAQ,CAC5B+D,eAAe,CAAEnE,QAAQ,CAACK,cAAc,CACxC+D,eAAe,CAAEtE,cACnB,CAAC,CAED,KAAM,CAAAD,iBAAiB,CAACmE,WAAW,CAAC,CACpCK,WAAW,CAAC,CAAC,CACf,CAAE,MAAOxB,GAAG,CAAE,CACZpC,QAAQ,CAACoC,GAAG,CAACyB,OAAO,EAAI,6BAA6B,CAAC,CACxD,CAAC,OAAS,CACR/D,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAED,KAAM,CAAA8D,WAAW,CAAGA,CAAA,GAAM,CACxB,GAAI,CAAC/D,SAAS,EAAI,CAACQ,mBAAmB,CAAE,CACtCb,WAAW,CAAC,CAAEC,IAAI,CAAE,EAAE,CAAEC,WAAW,CAAE,SAAS,CAAEC,QAAQ,CAAE,CAAC,CAAEC,cAAc,CAAE,cAAe,CAAC,CAAC,CAC9FI,QAAQ,CAAC,EAAE,CAAC,CACZE,cAAc,CAAC,CAAC,CAAC,CACjBE,iBAAiB,CAAC,IAAI,CAAC,CACvBjB,OAAO,CAAC,CAAC,CACX,CACF,CAAC,CAED,KAAM,CAAA2E,UAAU,CAAGA,CAAA,GAAM,CACvB5D,cAAc,CAAC,CAAC,CAAC,CACjBE,iBAAiB,CAAC,IAAI,CAAC,CACvBJ,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAED,GAAI,CAACd,MAAM,CAAE,MAAO,KAAI,CAExB,mBACEL,IAAA,QAAKkF,SAAS,CAAC,sFAAsF,CAAAC,QAAA,cACnGjF,KAAA,QAAKgF,SAAS,CAAC,6GAA6G,CAAAC,QAAA,eAE1HjF,KAAA,QAAKgF,SAAS,CAAC,yGAAyG,CAAAC,QAAA,eACtHjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,8HAA8H,CAAAC,QAAA,cAC3InF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,UAAU,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACtD,CAAC,cACNhF,KAAA,QAAAiF,QAAA,eACEnF,IAAA,OAAIkF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACzEnF,IAAA,MAAGkF,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iDAA+C,CAAG,CAAC,EACrF,CAAC,EACH,CAAC,cACNnF,IAAA,CAACN,MAAM,EACL0F,OAAO,CAAC,OAAO,CACfjB,IAAI,CAAC,MAAM,CACXkB,OAAO,CAAEN,WAAY,CACrBO,QAAQ,CAAEtE,SAAS,EAAIQ,mBAAoB,CAC3C0D,SAAS,CAAC,+EAA+E,CAAAC,QAAA,cAEzFnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,GAAG,CAACuD,IAAI,CAAE,EAAG,CAAE,CAAC,CACrB,CAAC,EACN,CAAC,cAGNnE,IAAA,QAAKkF,SAAS,CAAC,4BAA4B,CAAAC,QAAA,cACzCjF,KAAA,QAAKgF,SAAS,CAAC,4CAA4C,CAAAC,QAAA,eACzDjF,KAAA,QAAKgF,SAAS,CAAE,iDAAiD9D,WAAW,EAAI,CAAC,CAAG,cAAc,CAAG,uBAAuB,EAAG,CAAA+D,QAAA,eAC7HnF,IAAA,QAAKkF,SAAS,CAAE,qGACd9D,WAAW,EAAI,CAAC,CACZ,wDAAwD,CACxD,gCAAgC,EACnC,CAAA+D,QAAA,CAAC,GAEJ,CAAK,CAAC,cACNnF,IAAA,SAAMkF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,EACxD,CAAC,cACNnF,IAAA,QAAKkF,SAAS,CAAE,qDACd9D,WAAW,EAAI,CAAC,CAAG,YAAY,CAAG,UAAU,EAC3C,CAAM,CAAC,cACVlB,KAAA,QAAKgF,SAAS,CAAE,iDAAiD9D,WAAW,EAAI,CAAC,CAAG,cAAc,CAAG,uBAAuB,EAAG,CAAA+D,QAAA,eAC7HnF,IAAA,QAAKkF,SAAS,CAAE,qGACd9D,WAAW,EAAI,CAAC,CACZ,wDAAwD,CACxD,gCAAgC,EACnC,CAAA+D,QAAA,CAAC,GAEJ,CAAK,CAAC,cACNnF,IAAA,SAAMkF,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,kBAAgB,CAAM,CAAC,EAC/D,CAAC,EACH,CAAC,CACH,CAAC,cAGNnF,IAAA,QAAKkF,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAC1D/D,WAAW,GAAK,CAAC,cAChBpB,IAAA,CAACuF,iBAAiB,EAChB7E,QAAQ,CAAEA,QAAS,CACnBgB,YAAY,CAAEA,YAAa,CAC3BM,eAAe,CAAEA,eAAgB,CACjCC,iBAAiB,CAAEA,iBAAkB,CACrCuD,aAAa,CAAEtD,iBAAkB,CACjCuD,QAAQ,CAAElB,YAAa,CACvB/C,mBAAmB,CAAEA,mBAAoB,CACzCN,KAAK,CAAEA,KAAM,CACbT,gBAAgB,CAAEA,gBAAiB,CACpC,CAAC,cAEFT,IAAA,CAAC0F,WAAW,EACVpE,cAAc,CAAEA,cAAe,CAC/BmE,QAAQ,CAAElB,YAAa,CACvBoB,MAAM,CAAEV,UAAW,CACnBjE,SAAS,CAAEA,SAAU,CACrBE,KAAK,CAAEA,KAAM,CACd,CACF,CACE,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAqE,iBAAiB,CAAGK,KAAA,MAAC,CAAElF,QAAQ,CAAEgB,YAAY,CAAEM,eAAe,CAAEC,iBAAiB,CAAEuD,aAAa,CAAEC,QAAQ,CAAEjE,mBAAmB,CAAEN,KAAK,CAAET,gBAAiB,CAAC,CAAAmF,KAAA,oBAC9J1F,KAAA,SAAMuF,QAAQ,CAAEA,QAAS,CAACP,SAAS,CAAC,WAAW,CAAAC,QAAA,eAE7CnF,IAAA,QAAKkF,SAAS,CAAC,kFAAkF,CAAAC,QAAA,cAC/FjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9EnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,WAAW,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACvD,CAAC,cACNhF,KAAA,QAAAiF,QAAA,eACEnF,IAAA,MAAGkF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,aAAW,CAAG,CAAC,cAC5DnF,IAAA,MAAGkF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAE1E,gBAAgB,CAAI,CAAC,EAChE,CAAC,EACH,CAAC,CACH,CAAC,CAGLS,KAAK,eACJlB,IAAA,QAAKkF,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC5EjF,KAAA,QAAKgF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAAE,CAAC,cACrCnE,IAAA,SAAMkF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEjE,KAAK,CAAO,CAAC,EACzC,CAAC,CACH,CACN,cAEDhB,KAAA,QAAKgF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExBjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,UAAOkF,SAAS,CAAC,6CAA6C,CAAAC,QAAA,CAAC,gBAAc,CAAO,CAAC,cACrFnF,IAAA,CAACL,KAAK,EACJiD,IAAI,CAAC,MAAM,CACXjB,KAAK,CAAEjB,QAAQ,CAACE,IAAK,CACrBiF,QAAQ,CAAGrB,CAAC,EAAKgB,aAAa,CAAC,MAAM,CAAEhB,CAAC,CAACsB,MAAM,CAACnE,KAAK,CAAE,CACvDoE,WAAW,CAAC,4BAA4B,CACxCT,QAAQ,CAAE9D,mBAAoB,CAC9B0D,SAAS,CAAC,uBAAuB,CACjCc,QAAQ,MACT,CAAC,EACC,CAAC,cAGN9F,KAAA,QAAKgF,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpEjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,OAAO,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACnD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,EAC1E,CAAC,cAENjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,UAAOkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,WAAS,CAAO,CAAC,cAC9EnF,IAAA,CAACJ,MAAM,EACL+B,KAAK,CAAEjB,QAAQ,CAACI,QAAS,CACzB+E,QAAQ,CAAGlE,KAAK,EAAK6D,aAAa,CAAC,UAAU,CAAE7D,KAAK,CAAE,CACtDsE,OAAO,CAAEjE,eAAgB,CACzBsD,QAAQ,CAAE9D,mBAAoB,CAC9B0D,SAAS,CAAC,aAAa,CACxB,CAAC,EACC,CAAC,cAENhF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBnF,IAAA,UAAOkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,uBAAqB,CAAO,CAAC,cAC1FnF,IAAA,CAACJ,MAAM,EACL+B,KAAK,CAAEjB,QAAQ,CAACK,cAAe,CAC/B8E,QAAQ,CAAGlE,KAAK,EAAK6D,aAAa,CAAC,gBAAgB,CAAE7D,KAAK,CAAE,CAC5DsE,OAAO,CAAEhE,iBAAkB,CAC3BqD,QAAQ,CAAE9D,mBAAoB,CAC9B0D,SAAS,CAAC,aAAa,CACxB,CAAC,EACC,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,cAGNhF,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,QAAQ,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACpD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,EACpE,CAAC,cACNnF,IAAA,QAAKkF,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEzD,YAAY,CAACwE,GAAG,CAAEtD,IAAI,eACrB5C,IAAA,QAEEkF,SAAS,CAAE,4FACTxE,QAAQ,CAACG,WAAW,GAAK+B,IAAI,CAACjB,KAAK,CAC/B,oDAAoD,CACpD,0DAA0D,EAC7D,CACH0D,OAAO,CAAEA,CAAA,GAAMG,aAAa,CAAC,aAAa,CAAE5C,IAAI,CAACjB,KAAK,CAAE,CAAAwD,QAAA,cAExDjF,KAAA,QAAKgF,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCnF,IAAA,QAAKkF,SAAS,CAAE,aAAatC,IAAI,CAACb,KAAK,8GAA+G,CAAAoD,QAAA,cACpJnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAEgC,IAAI,CAACd,IAAK,CAACqC,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACvD,CAAC,cACNhF,KAAA,QAAKgF,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BnF,IAAA,OAAIkF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAEvC,IAAI,CAAChB,KAAK,CAAK,CAAC,cACzE5B,IAAA,MAAGkF,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAEvC,IAAI,CAACf,WAAW,CAAI,CAAC,EACrF,CAAC,CACLnB,QAAQ,CAACG,WAAW,GAAK+B,IAAI,CAACjB,KAAK,eAClC3B,IAAA,QAAKkF,SAAS,CAAC,gFAAgF,CAAAC,QAAA,cAC7FnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,OAAO,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAChE,CACN,EACE,CAAC,EArBDtC,IAAI,CAACjB,KAsBP,CACN,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,cAGN3B,IAAA,QAAKkF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9EnF,IAAA,CAACN,MAAM,EACLkD,IAAI,CAAC,QAAQ,CACbuB,IAAI,CAAC,IAAI,CACTmB,QAAQ,CAAE9D,mBAAmB,EAAI,CAACd,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAE,CACvD6D,QAAQ,CAAE3E,mBAAmB,CAAG,SAAS,CAAG,UAAW,CACvD4E,YAAY,CAAC,MAAM,CACnBlB,SAAS,CAAE,kKACT1D,mBAAmB,CAAG,eAAe,CAAG,EAAE,EACzC,CAAA2D,QAAA,CAEF3D,mBAAmB,CAAG,uBAAuB,CAAG,qBAAqB,CAChE,CAAC,CACN,CAAC,EACF,CAAC,EACR,CAED;AACA,KAAM,CAAAkE,WAAW,CAAGW,KAAA,OAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,IAAC,CAAEvF,cAAc,CAAEmE,QAAQ,CAAEE,MAAM,CAAE3E,SAAS,CAAEE,KAAM,CAAC,CAAAmF,KAAA,oBACzEnG,KAAA,QAAKgF,SAAS,CAAC,WAAW,CAAAC,QAAA,EAEvBjE,KAAK,eACJlB,IAAA,QAAKkF,SAAS,CAAC,+DAA+D,CAAAC,QAAA,cAC5EjF,KAAA,QAAKgF,SAAS,CAAC,0CAA0C,CAAAC,QAAA,eACvDnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAAE,CAAC,cACrCnE,IAAA,SAAMkF,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAEjE,KAAK,CAAO,CAAC,EACzC,CAAC,CACH,CACN,cAGDhB,KAAA,QAAKgF,SAAS,CAAC,8GAA8G,CAAAC,QAAA,eAC3HjF,KAAA,QAAKgF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CnF,IAAA,QAAKkF,SAAS,CAAE,aAAa,CAAA5D,cAAc,SAAdA,cAAc,kBAAAgF,oBAAA,CAAdhF,cAAc,CAAEsB,IAAI,UAAA0D,oBAAA,iBAApBA,oBAAA,CAAsBvE,KAAK,GAAI,aAAa,yDAA0D,CAAAoD,QAAA,cACjInF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAE,CAAAU,cAAc,SAAdA,cAAc,kBAAAiF,qBAAA,CAAdjF,cAAc,CAAEsB,IAAI,UAAA2D,qBAAA,iBAApBA,qBAAA,CAAsBzE,IAAI,GAAI,QAAS,CAACqC,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACpF,CAAC,cACNhF,KAAA,QAAAiF,QAAA,eACEnF,IAAA,OAAIkF,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAE7D,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEV,IAAI,CAAK,CAAC,cAC9EZ,IAAA,MAAGkF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAE7D,cAAc,SAAdA,cAAc,kBAAAkF,qBAAA,CAAdlF,cAAc,CAAEsB,IAAI,UAAA4D,qBAAA,iBAApBA,qBAAA,CAAsB5E,KAAK,CAAI,CAAC,EAC3E,CAAC,EACH,CAAC,cAEN1B,KAAA,QAAKgF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjF,KAAA,QAAKgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDnF,IAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAE7D,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEuB,iBAAiB,CAAM,CAAC,cAC1F7C,IAAA,QAAKkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,EAClE,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDnF,IAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAE7D,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEyB,cAAc,CAAM,CAAC,cACvF/C,IAAA,QAAKkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,OAAK,CAAK,CAAC,EACnE,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDnF,IAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAE,CAAA7D,cAAc,SAAdA,cAAc,kBAAAmF,qBAAA,CAAdnF,cAAc,CAAE2B,MAAM,UAAAwD,qBAAA,iBAAtBA,qBAAA,CAAwBK,MAAM,GAAI,CAAC,CAAM,CAAC,cAC5F9G,IAAA,QAAKkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,QAAM,CAAK,CAAC,EACpE,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDnF,IAAA,QAAKkF,SAAS,CAAC,iCAAiC,CAAAC,QAAA,CAAE,CAAA7D,cAAc,SAAdA,cAAc,kBAAAoF,qBAAA,CAAdpF,cAAc,CAAE+B,YAAY,UAAAqD,qBAAA,iBAA5BA,qBAAA,CAA8BI,MAAM,GAAI,CAAC,CAAM,CAAC,cAClG9G,IAAA,QAAKkF,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,cAAY,CAAK,CAAC,EAC1E,CAAC,EACH,CAAC,EACH,CAAC,cAENjF,KAAA,QAAKgF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDjF,KAAA,QAAKgF,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpEjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,iEAAiE,CAAAC,QAAA,cAC9EnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,WAAW,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACvD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,EACtE,CAAC,cACNnF,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB7D,cAAc,SAAdA,cAAc,kBAAAqF,sBAAA,CAAdrF,cAAc,CAAE2B,MAAM,UAAA0D,sBAAA,iBAAtBA,sBAAA,CAAwBT,GAAG,CAAC,CAACa,KAAK,CAAEC,KAAK,gBACxC9G,KAAA,QAAiBgF,SAAS,CAAC,6GAA6G,CAAAC,QAAA,eACtInF,IAAA,QAAKkF,SAAS,CAAC,8IAA8I,CAAAC,QAAA,CAC1J6B,KAAK,CAAG,CAAC,CACP,CAAC,cACNhH,IAAA,SAAMkF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAE4B,KAAK,CAAO,CAAC,GAJpDC,KAKL,CACN,CAAC,CACC,CAAC,EACH,CAAC,cAGN9G,KAAA,QAAKgF,SAAS,CAAC,uDAAuD,CAAAC,QAAA,eACpEjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,QAAKkF,SAAS,CAAC,kEAAkE,CAAAC,QAAA,cAC/EnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,MAAM,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CAClD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,EAChF,CAAC,cACNnF,IAAA,QAAKkF,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAClC7D,cAAc,SAAdA,cAAc,kBAAAsF,sBAAA,CAAdtF,cAAc,CAAE+B,YAAY,UAAAuD,sBAAA,iBAA5BA,sBAAA,CAA8BV,GAAG,CAAC,CAAC5B,IAAI,CAAE0C,KAAK,gBAC7ChH,IAAA,SAAkBkF,SAAS,CAAC,oIAAoI,CAAAC,QAAA,CAC7Jb,IAAI,EADI0C,KAEL,CACP,CAAC,CACC,CAAC,CAGL,CAAA1F,cAAc,SAAdA,cAAc,kBAAAuF,qBAAA,CAAdvF,cAAc,CAAE6B,mBAAmB,UAAA0D,qBAAA,iBAAnCA,qBAAA,CAAqCC,MAAM,EAAG,CAAC,eAC9C5G,KAAA,QAAKgF,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CjF,KAAA,QAAKgF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CnF,IAAA,QAAKkF,SAAS,CAAC,mEAAmE,CAAAC,QAAA,cAChFnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,OAAO,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACnD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,+BAA+B,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,EACpE,CAAC,cACNnF,IAAA,QAAKkF,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB7D,cAAc,CAAC6B,mBAAmB,CAAC+C,GAAG,CAAC,CAACe,GAAG,CAAED,KAAK,gBACjD9G,KAAA,QAAiBgF,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAC/EnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,qCAAqC,CAAE,CAAC,cACrFlF,IAAA,SAAAmF,QAAA,CAAO8B,GAAG,CAAO,CAAC,GAFVD,KAGL,CACN,CAAC,CACC,CAAC,EACH,CACN,EACE,CAAC,EACH,CAAC,cAGN9G,KAAA,QAAKgF,SAAS,CAAC,8GAA8G,CAAAC,QAAA,eAC3HjF,KAAA,QAAKgF,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CnF,IAAA,QAAKkF,SAAS,CAAC,oEAAoE,CAAAC,QAAA,cACjFnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,UAAU,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,YAAY,CAAE,CAAC,CACtD,CAAC,cACNlF,IAAA,OAAIkF,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,gCAA8B,CAAI,CAAC,EACvF,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,kDAAgD,CAAM,CAAC,EAC1D,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,8CAA4C,CAAM,CAAC,EACtD,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,uDAAqD,CAAM,CAAC,EAC/D,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,6CAA2C,CAAM,CAAC,EACrD,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,+CAA6C,CAAM,CAAC,EACvD,CAAC,cACNjF,KAAA,QAAKgF,SAAS,CAAC,kDAAkD,CAAAC,QAAA,eAC/DnF,IAAA,CAACH,IAAI,EAACe,IAAI,CAAC,aAAa,CAACuD,IAAI,CAAE,EAAG,CAACe,SAAS,CAAC,gCAAgC,CAAE,CAAC,cAChFlF,IAAA,SAAAmF,QAAA,CAAM,2CAAyC,CAAM,CAAC,EACnD,CAAC,EACH,CAAC,EACH,CAAC,cAENjF,KAAA,QAAKgF,SAAS,CAAC,+DAA+D,CAAAC,QAAA,eAC5EjF,KAAA,QAAKgF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtCnF,IAAA,CAACN,MAAM,EACL0F,OAAO,CAAC,SAAS,CACjBjB,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEM,MAAO,CAChBL,QAAQ,CAAEtE,SAAU,CACpBmF,QAAQ,CAAC,WAAW,CACpBC,YAAY,CAAC,MAAM,CACnBlB,SAAS,CAAC,UAAU,CAAAC,QAAA,CACrB,uBAED,CAAQ,CAAC,cACTnF,IAAA,CAACF,mBAAmB,EAAC4E,WAAW,CAAEpD,cAAe,CAAE,CAAC,EACjD,CAAC,cACNtB,IAAA,CAACN,MAAM,EACLyE,IAAI,CAAC,IAAI,CACTkB,OAAO,CAAEI,QAAS,CAClBH,QAAQ,CAAEtE,SAAU,CACpBmF,QAAQ,CAAEnF,SAAS,CAAG,SAAS,CAAG,QAAS,CAC3CoF,YAAY,CAAC,MAAM,CACnBlB,SAAS,CAAE,kKACTlE,SAAS,CAAG,eAAe,CAAG,EAAE,EAC/B,CAAAmE,QAAA,CAEFnE,SAAS,CAAG,qBAAqB,CAAG,mBAAmB,CAClD,CAAC,EACN,CAAC,EACH,CAAC,EACP,CAED,cAAe,CAAAb,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}