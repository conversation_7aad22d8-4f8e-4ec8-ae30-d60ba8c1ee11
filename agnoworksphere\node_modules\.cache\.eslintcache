[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx": "84", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx": "85", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx": "86", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx": "87", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js": "88", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js": "89", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js": "90", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx": "91", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js": "92", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js": "93", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js": "94", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx": "95", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx": "96", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js": "97", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx": "98", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js": "99", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx": "100", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx": "101", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx": "102", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js": "103", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js": "104", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx": "105", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx": "106", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx": "107", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx": "108", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\EnhancedProjectCreationWizard.jsx": "109", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfigurationInterface.jsx": "110", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectOverviewEditor.jsx": "111", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TechStackDisplay.jsx": "112", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\WorkflowManagement.jsx": "113", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfirmationSummary.jsx": "114", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TaskChecklistSystem.jsx": "115", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Slider.jsx": "116", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RichTextEditor.jsx": "117", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\DatePicker.jsx": "118", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Toggle.jsx": "119", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Modal.jsx": "120"}, {"size": 1396, "mtime": 1754034229833, "results": "121", "hashOfConfig": "122"}, {"size": 977, "mtime": 1754033081943, "results": "123", "hashOfConfig": "122"}, {"size": 2842, "mtime": 1754286869549, "results": "124", "hashOfConfig": "122"}, {"size": 2279, "mtime": 1754126212460, "results": "125", "hashOfConfig": "122"}, {"size": 263, "mtime": 1753662156000, "results": "126", "hashOfConfig": "122"}, {"size": 2597, "mtime": 1753662156000, "results": "127", "hashOfConfig": "122"}, {"size": 1443, "mtime": 1753896368000, "results": "128", "hashOfConfig": "122"}, {"size": 1851, "mtime": 1753662960000, "results": "129", "hashOfConfig": "122"}, {"size": 2221, "mtime": 1754033188241, "results": "130", "hashOfConfig": "122"}, {"size": 25724, "mtime": 1754360767848, "results": "131", "hashOfConfig": "122"}, {"size": 12772, "mtime": 1754200544518, "results": "132", "hashOfConfig": "122"}, {"size": 19565, "mtime": 1754126131895, "results": "133", "hashOfConfig": "122"}, {"size": 8178, "mtime": 1754043014618, "results": "134", "hashOfConfig": "122"}, {"size": 12073, "mtime": 1754036144487, "results": "135", "hashOfConfig": "122"}, {"size": 15520, "mtime": 1754316508370, "results": "136", "hashOfConfig": "122"}, {"size": 9015, "mtime": 1754287684311, "results": "137", "hashOfConfig": "122"}, {"size": 32913, "mtime": 1754326468461, "results": "138", "hashOfConfig": "122"}, {"size": 619, "mtime": 1753662156000, "results": "139", "hashOfConfig": "122"}, {"size": 3229, "mtime": 1753931434000, "results": "140", "hashOfConfig": "122"}, {"size": 15516, "mtime": 1754037223798, "results": "141", "hashOfConfig": "122"}, {"size": 3119, "mtime": 1753662156000, "results": "142", "hashOfConfig": "122"}, {"size": 9775, "mtime": 1753662156000, "results": "143", "hashOfConfig": "122"}, {"size": 1661, "mtime": 1753662960000, "results": "144", "hashOfConfig": "122"}, {"size": 8345, "mtime": 1754040316710, "results": "145", "hashOfConfig": "122"}, {"size": 2069, "mtime": 1753662960000, "results": "146", "hashOfConfig": "122"}, {"size": 4544, "mtime": 1754316602745, "results": "147", "hashOfConfig": "122"}, {"size": 1270, "mtime": 1753662960000, "results": "148", "hashOfConfig": "122"}, {"size": 12175, "mtime": 1754056135125, "results": "149", "hashOfConfig": "122"}, {"size": 14625, "mtime": 1754121145710, "results": "150", "hashOfConfig": "122"}, {"size": 3061, "mtime": 1753662960000, "results": "151", "hashOfConfig": "122"}, {"size": 14179, "mtime": 1754289123461, "results": "152", "hashOfConfig": "122"}, {"size": 11396, "mtime": 1754289682949, "results": "153", "hashOfConfig": "122"}, {"size": 7293, "mtime": 1754121209630, "results": "154", "hashOfConfig": "122"}, {"size": 4639, "mtime": 1754041421031, "results": "155", "hashOfConfig": "122"}, {"size": 4319, "mtime": 1753662960000, "results": "156", "hashOfConfig": "122"}, {"size": 3173, "mtime": 1754300540067, "results": "157", "hashOfConfig": "122"}, {"size": 11197, "mtime": 1754052040562, "results": "158", "hashOfConfig": "122"}, {"size": 8439, "mtime": 1754123709182, "results": "159", "hashOfConfig": "122"}, {"size": 2962, "mtime": 1753662960000, "results": "160", "hashOfConfig": "122"}, {"size": 4192, "mtime": 1753662960000, "results": "161", "hashOfConfig": "122"}, {"size": 2269, "mtime": 1753662960000, "results": "162", "hashOfConfig": "122"}, {"size": 6524, "mtime": 1754123731298, "results": "163", "hashOfConfig": "122"}, {"size": 5011, "mtime": 1753662960000, "results": "164", "hashOfConfig": "122"}, {"size": 5667, "mtime": 1753662960000, "results": "165", "hashOfConfig": "122"}, {"size": 6178, "mtime": 1753662960000, "results": "166", "hashOfConfig": "122"}, {"size": 3646, "mtime": 1754043560517, "results": "167", "hashOfConfig": "122"}, {"size": 8113, "mtime": 1754043500588, "results": "168", "hashOfConfig": "122"}, {"size": 6935, "mtime": 1753662960000, "results": "169", "hashOfConfig": "122"}, {"size": 5068, "mtime": 1753662960000, "results": "170", "hashOfConfig": "122"}, {"size": 3503, "mtime": 1754043686958, "results": "171", "hashOfConfig": "122"}, {"size": 11618, "mtime": 1754120839603, "results": "172", "hashOfConfig": "122"}, {"size": 12779, "mtime": 1753660406000, "results": "173", "hashOfConfig": "122"}, {"size": 18772, "mtime": 1753660406000, "results": "174", "hashOfConfig": "122"}, {"size": 16373, "mtime": 1753660406000, "results": "175", "hashOfConfig": "122"}, {"size": 20926, "mtime": 1754121648073, "results": "176", "hashOfConfig": "122"}, {"size": 40239, "mtime": 1754303788911, "results": "177", "hashOfConfig": "122"}, {"size": 5370, "mtime": 1753663554000, "results": "178", "hashOfConfig": "122"}, {"size": 5637, "mtime": 1753663554000, "results": "179", "hashOfConfig": "122"}, {"size": 1689, "mtime": 1753663554000, "results": "180", "hashOfConfig": "122"}, {"size": 5556, "mtime": 1753663554000, "results": "181", "hashOfConfig": "122"}, {"size": 15986, "mtime": 1754296296708, "results": "182", "hashOfConfig": "122"}, {"size": 14780, "mtime": 1754317072373, "results": "183", "hashOfConfig": "122"}, {"size": 19464, "mtime": 1753667086000, "results": "184", "hashOfConfig": "122"}, {"size": 9333, "mtime": 1754120749717, "results": "185", "hashOfConfig": "122"}, {"size": 13135, "mtime": 1754121313818, "results": "186", "hashOfConfig": "122"}, {"size": 13718, "mtime": 1753660406000, "results": "187", "hashOfConfig": "122"}, {"size": 2946, "mtime": 1753916458000, "results": "188", "hashOfConfig": "122"}, {"size": 5117, "mtime": 1753916458000, "results": "189", "hashOfConfig": "122"}, {"size": 3744, "mtime": 1753916090000, "results": "190", "hashOfConfig": "122"}, {"size": 9103, "mtime": 1754360825970, "results": "191", "hashOfConfig": "122"}, {"size": 7239, "mtime": 1754324730283, "results": "192", "hashOfConfig": "122"}, {"size": 5908, "mtime": 1754286269451, "results": "193", "hashOfConfig": "122"}, {"size": 139, "mtime": 1753662156000, "results": "194", "hashOfConfig": "122"}, {"size": 8156, "mtime": 1754123202462, "results": "195", "hashOfConfig": "122"}, {"size": 5802, "mtime": 1753916090000, "results": "196", "hashOfConfig": "122"}, {"size": 4753, "mtime": 1753662156000, "results": "197", "hashOfConfig": "122"}, {"size": 329, "mtime": 1753662156000, "results": "198", "hashOfConfig": "122"}, {"size": 6210, "mtime": 1754041438145, "results": "199", "hashOfConfig": "122"}, {"size": 7754, "mtime": 1754324345552, "results": "200", "hashOfConfig": "122"}, {"size": 13056, "mtime": 1754196793734, "results": "201", "hashOfConfig": "122"}, {"size": 10550, "mtime": 1754332244443, "results": "202", "hashOfConfig": "122"}, {"size": 623, "mtime": 1753979082425, "results": "203", "hashOfConfig": "122"}, {"size": 6960, "mtime": 1754290875021, "results": "204", "hashOfConfig": "122"}, {"size": 8078, "mtime": 1754026444720, "results": "205", "hashOfConfig": "122"}, {"size": 2645, "mtime": 1754026405389, "results": "206", "hashOfConfig": "122"}, {"size": 8796, "mtime": 1754030789819, "results": "207", "hashOfConfig": "122"}, {"size": 27243, "mtime": 1754290751151, "results": "208", "hashOfConfig": "122"}, {"size": 5668, "mtime": 1754050276943, "results": "209", "hashOfConfig": "122"}, {"size": 10660, "mtime": 1754318186933, "results": "210", "hashOfConfig": "122"}, {"size": 8055, "mtime": 1754050911094, "results": "211", "hashOfConfig": "122"}, {"size": 24107, "mtime": 1754055910235, "results": "212", "hashOfConfig": "122"}, {"size": 18642, "mtime": 1754289790236, "results": "213", "hashOfConfig": "122"}, {"size": 4832, "mtime": 1754284927004, "results": "214", "hashOfConfig": "122"}, {"size": 11155, "mtime": 1754289886219, "results": "215", "hashOfConfig": "122"}, {"size": 15845, "mtime": 1754195084404, "results": "216", "hashOfConfig": "122"}, {"size": 15642, "mtime": 1754195149865, "results": "217", "hashOfConfig": "122"}, {"size": 6775, "mtime": 1754324325311, "results": "218", "hashOfConfig": "122"}, {"size": 1009, "mtime": 1754286836782, "results": "219", "hashOfConfig": "122"}, {"size": 5819, "mtime": 1754302971651, "results": "220", "hashOfConfig": "122"}, {"size": 8994, "mtime": 1754289962090, "results": "221", "hashOfConfig": "122"}, {"size": 14588, "mtime": 1754318206343, "results": "222", "hashOfConfig": "122"}, {"size": 20788, "mtime": 1754301009875, "results": "223", "hashOfConfig": "122"}, {"size": 13482, "mtime": 1754300981568, "results": "224", "hashOfConfig": "122"}, {"size": 2628, "mtime": 1754298424105, "results": "225", "hashOfConfig": "122"}, {"size": 7571, "mtime": 1754301031662, "results": "226", "hashOfConfig": "122"}, {"size": 15107, "mtime": 1754326339751, "results": "227", "hashOfConfig": "122"}, {"size": 20183, "mtime": 1754326527281, "results": "228", "hashOfConfig": "122"}, {"size": 6593, "mtime": 1754326501301, "results": "229", "hashOfConfig": "122"}, {"size": 8226, "mtime": 1754332124691, "results": "230", "hashOfConfig": "122"}, {"size": 12450, "mtime": 1754331553337, "results": "231", "hashOfConfig": "122"}, {"size": 15990, "mtime": 1754331662691, "results": "232", "hashOfConfig": "122"}, {"size": 23740, "mtime": 1754331781381, "results": "233", "hashOfConfig": "122"}, {"size": 22424, "mtime": 1754331881155, "results": "234", "hashOfConfig": "122"}, {"size": 21639, "mtime": 1754360445977, "results": "235", "hashOfConfig": "122"}, {"size": 22690, "mtime": 1754360457151, "results": "236", "hashOfConfig": "122"}, {"size": 2312, "mtime": 1754331472937, "results": "237", "hashOfConfig": "122"}, {"size": 8044, "mtime": 1754331598347, "results": "238", "hashOfConfig": "122"}, {"size": 2581, "mtime": 1754331498421, "results": "239", "hashOfConfig": "122"}, {"size": 2129, "mtime": 1754331485404, "results": "240", "hashOfConfig": "122"}, {"size": 2383, "mtime": 1754331687394, "results": "241", "hashOfConfig": "122"}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["602", "603", "604", "605"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", ["606", "607"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["608"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", ["609", "610", "611", "612"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", ["613", "614", "615", "616", "617", "618"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", ["619"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", ["620", "621", "622"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["623", "624", "625", "626"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["627", "628", "629"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", ["630", "631", "632", "633", "634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["645", "646", "647"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", ["648"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", ["649", "650"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx", ["651", "652"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js", ["653", "654"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js", ["655"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js", ["656"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateOrganizationModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realApiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\teamService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\notificationService.js", ["657"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\analytics\\index.jsx", ["658"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\billing\\index.jsx", ["659"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\realAuthService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\hooks\\useUserProfile.js", ["660"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\NotificationDropdown.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateTaskModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\AIReportModal.jsx", ["661"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiReportService.js", ["662"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\projectEventService.js", ["663"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\charts\\ReportCharts.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateAIProjectModal.jsx", ["664", "665", "666", "667", "668"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\EnhancedCreateAIProjectModal.jsx", ["669"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\ProjectExportButton.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\EnhancedProjectCreationWizard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfigurationInterface.jsx", ["670", "671", "672", "673"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectOverviewEditor.jsx", ["674"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TechStackDisplay.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\WorkflowManagement.jsx", ["675", "676", "677", "678"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\ProjectConfirmationSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\project\\TaskChecklistSystem.jsx", ["679", "680", "681", "682", "683", "684", "685", "686"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Slider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RichTextEditor.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\DatePicker.jsx", ["687", "688", "689"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Toggle.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Modal.jsx", [], [], {"ruleId": "690", "severity": 1, "message": "691", "line": 20, "column": 11, "nodeType": "692", "messageId": "693", "endLine": 20, "endColumn": 15}, {"ruleId": "690", "severity": 1, "message": "694", "line": 61, "column": 19, "nodeType": "692", "messageId": "693", "endLine": 61, "endColumn": 29}, {"ruleId": "690", "severity": 1, "message": "695", "line": 287, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 287, "endColumn": 21}, {"ruleId": "690", "severity": 1, "message": "696", "line": 292, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 292, "endColumn": 23}, {"ruleId": "690", "severity": 1, "message": "697", "line": 69, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 69, "endColumn": 17}, {"ruleId": "698", "severity": 1, "message": "699", "line": 191, "column": 6, "nodeType": "700", "endLine": 191, "endColumn": 52, "suggestions": "701"}, {"ruleId": "690", "severity": 1, "message": "702", "line": 22, "column": 27, "nodeType": "692", "messageId": "693", "endLine": 22, "endColumn": 45}, {"ruleId": "690", "severity": 1, "message": "703", "line": 8, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 8, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "704", "line": 19, "column": 5, "nodeType": "692", "messageId": "693", "endLine": 19, "endColumn": 22}, {"ruleId": "690", "severity": 1, "message": "705", "line": 23, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 23, "endColumn": 21}, {"ruleId": "690", "severity": 1, "message": "697", "line": 25, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 25, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "706", "line": 28, "column": 5, "nodeType": "692", "messageId": "693", "endLine": 28, "endColumn": 16}, {"ruleId": "690", "severity": 1, "message": "707", "line": 30, "column": 5, "nodeType": "692", "messageId": "693", "endLine": 30, "endColumn": 27}, {"ruleId": "690", "severity": 1, "message": "708", "line": 31, "column": 14, "nodeType": "692", "messageId": "693", "endLine": 31, "endColumn": 28}, {"ruleId": "690", "severity": 1, "message": "709", "line": 35, "column": 23, "nodeType": "692", "messageId": "693", "endLine": 35, "endColumn": 37}, {"ruleId": "690", "severity": 1, "message": "710", "line": 36, "column": 23, "nodeType": "692", "messageId": "693", "endLine": 36, "endColumn": 37}, {"ruleId": "698", "severity": 1, "message": "711", "line": 192, "column": 6, "nodeType": "700", "endLine": 192, "endColumn": 22, "suggestions": "712"}, {"ruleId": "690", "severity": 1, "message": "713", "line": 8, "column": 49, "nodeType": "692", "messageId": "693", "endLine": 8, "endColumn": 60}, {"ruleId": "690", "severity": 1, "message": "714", "line": 4, "column": 31, "nodeType": "692", "messageId": "693", "endLine": 4, "endColumn": 48}, {"ruleId": "690", "severity": 1, "message": "715", "line": 12, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 12, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "716", "line": 65, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 65, "endColumn": 31}, {"ruleId": "690", "severity": 1, "message": "697", "line": 11, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 11, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "717", "line": 12, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 12, "endColumn": 16}, {"ruleId": "690", "severity": 1, "message": "718", "line": 12, "column": 18, "nodeType": "692", "messageId": "693", "endLine": 12, "endColumn": 27}, {"ruleId": "690", "severity": 1, "message": "719", "line": 57, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 57, "endColumn": 18}, {"ruleId": "690", "severity": 1, "message": "720", "line": 9, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 9, "endColumn": 22}, {"ruleId": "721", "severity": 1, "message": "722", "line": 151, "column": 5, "nodeType": "723", "messageId": "724", "endLine": 169, "endColumn": 6}, {"ruleId": "690", "severity": 1, "message": "725", "line": 181, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 181, "endColumn": 21}, {"ruleId": "690", "severity": 1, "message": "726", "line": 11, "column": 34, "nodeType": "692", "messageId": "693", "endLine": 11, "endColumn": 57}, {"ruleId": "690", "severity": 1, "message": "727", "line": 18, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 18, "endColumn": 36}, {"ruleId": "698", "severity": 1, "message": "728", "line": 104, "column": 6, "nodeType": "700", "endLine": 104, "endColumn": 24, "suggestions": "729"}, {"ruleId": "698", "severity": 1, "message": "728", "line": 118, "column": 6, "nodeType": "700", "endLine": 118, "endColumn": 24, "suggestions": "730"}, {"ruleId": "690", "severity": 1, "message": "731", "line": 217, "column": 30, "nodeType": "692", "messageId": "693", "endLine": 217, "endColumn": 51}, {"ruleId": "690", "severity": 1, "message": "732", "line": 218, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 218, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "733", "line": 223, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 223, "endColumn": 20}, {"ruleId": "690", "severity": 1, "message": "734", "line": 224, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 224, "endColumn": 26}, {"ruleId": "690", "severity": 1, "message": "735", "line": 227, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 227, "endColumn": 30}, {"ruleId": "690", "severity": 1, "message": "736", "line": 248, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 248, "endColumn": 22}, {"ruleId": "690", "severity": 1, "message": "737", "line": 254, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 254, "endColumn": 28}, {"ruleId": "690", "severity": 1, "message": "738", "line": 265, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 265, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "739", "line": 274, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 274, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "740", "line": 289, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 289, "endColumn": 34}, {"ruleId": "690", "severity": 1, "message": "741", "line": 312, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 312, "endColumn": 33}, {"ruleId": "690", "severity": 1, "message": "697", "line": 20, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 20, "endColumn": 17}, {"ruleId": "698", "severity": 1, "message": "742", "line": 27, "column": 6, "nodeType": "700", "endLine": 27, "endColumn": 19, "suggestions": "743"}, {"ruleId": "690", "severity": 1, "message": "744", "line": 76, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 76, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "720", "line": 6, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 6, "endColumn": 22}, {"ruleId": "690", "severity": 1, "message": "745", "line": 11, "column": 29, "nodeType": "692", "messageId": "693", "endLine": 11, "endColumn": 49}, {"ruleId": "698", "severity": 1, "message": "746", "line": 32, "column": 6, "nodeType": "700", "endLine": 32, "endColumn": 30, "suggestions": "747"}, {"ruleId": "698", "severity": 1, "message": "748", "line": 135, "column": 6, "nodeType": "700", "endLine": 135, "endColumn": 62, "suggestions": "749"}, {"ruleId": "698", "severity": 1, "message": "750", "line": 151, "column": 6, "nodeType": "700", "endLine": 151, "endColumn": 8, "suggestions": "751"}, {"ruleId": "690", "severity": 1, "message": "752", "line": 124, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 124, "endColumn": 20}, {"ruleId": "753", "severity": 1, "message": "754", "line": 185, "column": 1, "nodeType": "755", "endLine": 193, "endColumn": 3}, {"ruleId": "753", "severity": 1, "message": "754", "line": 338, "column": 1, "nodeType": "755", "endLine": 342, "endColumn": 3}, {"ruleId": "753", "severity": 1, "message": "754", "line": 250, "column": 1, "nodeType": "755", "endLine": 260, "endColumn": 3}, {"ruleId": "690", "severity": 1, "message": "720", "line": 1, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 22}, {"ruleId": "698", "severity": 1, "message": "756", "line": 34, "column": 6, "nodeType": "700", "endLine": 34, "endColumn": 18, "suggestions": "757"}, {"ruleId": "698", "severity": 1, "message": "758", "line": 36, "column": 6, "nodeType": "700", "endLine": 36, "endColumn": 8, "suggestions": "759"}, {"ruleId": "698", "severity": 1, "message": "760", "line": 166, "column": 6, "nodeType": "700", "endLine": 166, "endColumn": 33, "suggestions": "761"}, {"ruleId": "698", "severity": 1, "message": "762", "line": 23, "column": 6, "nodeType": "700", "endLine": 23, "endColumn": 23, "suggestions": "763"}, {"ruleId": "753", "severity": 1, "message": "754", "line": 467, "column": 1, "nodeType": "755", "endLine": 471, "endColumn": 3}, {"ruleId": "753", "severity": 1, "message": "754", "line": 82, "column": 1, "nodeType": "755", "endLine": 89, "endColumn": 3}, {"ruleId": "690", "severity": 1, "message": "764", "line": 1, "column": 27, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 36}, {"ruleId": "690", "severity": 1, "message": "765", "line": 17, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 17, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "766", "line": 33, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 33, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "767", "line": 41, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 41, "endColumn": 26}, {"ruleId": "690", "severity": 1, "message": "768", "line": 217, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 217, "endColumn": 19}, {"ruleId": "690", "severity": 1, "message": "764", "line": 1, "column": 27, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 36}, {"ruleId": "690", "severity": 1, "message": "769", "line": 4, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 4, "endColumn": 13}, {"ruleId": "690", "severity": 1, "message": "770", "line": 8, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 8, "endColumn": 18}, {"ruleId": "690", "severity": 1, "message": "771", "line": 9, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 9, "endColumn": 18}, {"ruleId": "698", "severity": 1, "message": "772", "line": 93, "column": 6, "nodeType": "700", "endLine": 93, "endColumn": 14, "suggestions": "773"}, {"ruleId": "698", "severity": 1, "message": "774", "line": 37, "column": 6, "nodeType": "700", "endLine": 37, "endColumn": 16, "suggestions": "775"}, {"ruleId": "690", "severity": 1, "message": "776", "line": 1, "column": 27, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 33}, {"ruleId": "690", "severity": 1, "message": "764", "line": 1, "column": 35, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 44}, {"ruleId": "690", "severity": 1, "message": "777", "line": 76, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 76, "endColumn": 22}, {"ruleId": "690", "severity": 1, "message": "778", "line": 76, "column": 24, "nodeType": "692", "messageId": "693", "endLine": 76, "endColumn": 39}, {"ruleId": "690", "severity": 1, "message": "764", "line": 1, "column": 27, "nodeType": "692", "messageId": "693", "endLine": 1, "endColumn": 36}, {"ruleId": "690", "severity": 1, "message": "770", "line": 6, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 6, "endColumn": 18}, {"ruleId": "690", "severity": 1, "message": "779", "line": 8, "column": 8, "nodeType": "692", "messageId": "693", "endLine": 8, "endColumn": 13}, {"ruleId": "690", "severity": 1, "message": "780", "line": 92, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 92, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "781", "line": 112, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 112, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "782", "line": 122, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 122, "endColumn": 24}, {"ruleId": "690", "severity": 1, "message": "783", "line": 196, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 196, "endColumn": 25}, {"ruleId": "690", "severity": 1, "message": "784", "line": 259, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 259, "endColumn": 17}, {"ruleId": "690", "severity": 1, "message": "785", "line": 19, "column": 10, "nodeType": "692", "messageId": "693", "endLine": 19, "endColumn": 16}, {"ruleId": "690", "severity": 1, "message": "786", "line": 19, "column": 18, "nodeType": "692", "messageId": "693", "endLine": 19, "endColumn": 27}, {"ruleId": "690", "severity": 1, "message": "787", "line": 28, "column": 9, "nodeType": "692", "messageId": "693", "endLine": 28, "endColumn": 26}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'setMembers' is assigned a value but never used.", "'canEditCards' is assigned a value but never used.", "'canDeleteCards' is assigned a value but never used.", "'loading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'currentUser?.email', 'currentUser?.firstName', 'currentUser?.lastName', and 'userRole'. Either include them or remove the dependency array.", "ArrayExpression", ["788"], "'setSidebarExpanded' is assigned a value but never used.", "'authService' is defined but never used.", "'updateUserProfile' is assigned a value but never used.", "'currentUser' is assigned a value but never used.", "'userProfile' is assigned a value but never used.", "'availableOrganizations' is assigned a value but never used.", "'profileLoading' is assigned a value but never used.", "'setSearchValue' is assigned a value but never used.", "'setFilterValue' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'currentUser' and 'organizations.length'. Either include them or remove the dependency array.", ["789"], "'handleError' is defined but never used.", "'getSuggestedItems' is defined but never used.", "'showSuggestions' is assigned a value but never used.", "'handleAddSuggestedItem' is assigned a value but never used.", "'saving' is assigned a value but never used.", "'setSaving' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "'realApiService' is defined but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'refreshProjectsGlobally' is defined but never used.", "'isNotificationDropdownOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["790"], ["791"], "'setNotificationFilter' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'unreadCount' is assigned a value but never used.", "'highPriorityCount' is assigned a value but never used.", "'filteredNotifications' is assigned a value but never used.", "'markAllAsRead' is assigned a value but never used.", "'getNotificationIcon' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'formatTimestamp' is assigned a value but never used.", "'getRoleBasedNotifications' is assigned a value but never used.", "'handleNotificationAction' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTasks'. Either include it or remove the dependency array.", ["792"], "'priorityOptions' is assigned a value but never used.", "'setUseEnhancedWizard' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadTeamMembers'. Either include it or remove the dependency array.", ["793"], "React Hook useCallback has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["794"], "React Hook useEffect has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["795"], "'permissions' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'loadAnalyticsData'. Either include it or remove the dependency array.", ["796"], "React Hook useEffect has a missing dependency: 'loadBillingData'. Either include it or remove the dependency array.", ["797"], "React Hook useEffect has a missing dependency: 'loadUserProfile'. Either include it or remove the dependency array.", ["798"], "React Hook useEffect has a missing dependency: 'generateReport'. Either include it or remove the dependency array.", ["799"], "'useEffect' is defined but never used.", "'projectPreview' is assigned a value but never used.", "'teamSizeOptions' is assigned a value but never used.", "'experienceOptions' is assigned a value but never used.", "'handleBack' is assigned a value but never used.", "'Input' is defined but never used.", "'DatePicker' is defined but never used.", "'Textarea' is defined but never used.", "React Hook useEffect has a missing dependency: 'validateConfiguration'. Either include it or remove the dependency array.", ["800"], "React Hook useEffect has a missing dependency: 'validateOverview'. Either include it or remove the dependency array.", ["801"], "'useRef' is defined but never used.", "'draggedPhase' is assigned a value but never used.", "'setDraggedPhase' is assigned a value but never used.", "'Modal' is defined but never used.", "'isTaskModalOpen' is assigned a value but never used.", "'assigneeOptions' is assigned a value but never used.", "'categoryOptions' is assigned a value but never used.", "'toggleTaskStatus' is assigned a value but never used.", "'saveTask' is assigned a value but never used.", "'isOpen' is assigned a value but never used.", "'setIsOpen' is assigned a value but never used.", "'formatDisplayDate' is assigned a value but never used.", {"desc": "802", "fix": "803"}, {"desc": "804", "fix": "805"}, {"desc": "806", "fix": "807"}, {"desc": "808", "fix": "809"}, {"desc": "810", "fix": "811"}, {"desc": "812", "fix": "813"}, {"desc": "814", "fix": "815"}, {"desc": "816", "fix": "817"}, {"desc": "818", "fix": "819"}, {"desc": "820", "fix": "821"}, {"desc": "822", "fix": "823"}, {"desc": "824", "fix": "825"}, {"desc": "826", "fix": "827"}, {"desc": "828", "fix": "829"}, "Update the dependencies array to be: [currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", {"range": "830", "text": "831"}, "Update the dependencies array to be: [currentUser, location.state, organizations.length]", {"range": "832", "text": "833"}, "Update the dependencies array to be: [loadProjects, organization.id]", {"range": "834", "text": "835"}, "Update the dependencies array to be: [loadProjects, organization?.id]", {"range": "836", "text": "837"}, "Update the dependencies array to be: [loadTasks, project?.id]", {"range": "838", "text": "839"}, "Update the dependencies array to be: [isOpen, loadTeamMembers, organizationId]", {"range": "840", "text": "841"}, "Update the dependencies array to be: [defaultShortcuts, sequenceTimeout, keySequence]", {"range": "842", "text": "843"}, "Update the dependencies array to be: [defaultShortcuts]", {"range": "844", "text": "845"}, "Update the dependencies array to be: [loadAnalyticsData, timePeriod]", {"range": "846", "text": "847"}, "Update the dependencies array to be: [loadBillingData]", {"range": "848", "text": "849"}, "Update the dependencies array to be: [isAuthenticated, authUser, loadUserProfile]", {"range": "850", "text": "851"}, "Update the dependencies array to be: [generateReport, isOpen, project]", {"range": "852", "text": "853"}, "Update the dependencies array to be: [config, validateConfiguration]", {"range": "854", "text": "855"}, "Update the dependencies array to be: [overview, validateOverview]", {"range": "856", "text": "857"}, [6367, 6413], "[currentOrganization, searchQuery, roleFilter, currentUser?.firstName, currentUser?.lastName, currentUser?.email, userRole]", [7762, 7778], "[currentUser, location.state, organizations.length]", [3947, 3965], "[loadProjects, organization.id]", [4490, 4508], "[loadProjects, organization?.id]", [1142, 1155], "[loadTasks, project?.id]", [1128, 1152], "[isO<PERSON>, loadTeamMembers, organizationId]", [4586, 4642], "[defaultShortcuts, sequenceTimeout, keySequence]", [5058, 5060], "[defaultShortcuts]", [1168, 1180], "[loadAnalyticsData, timePeriod]", [1271, 1273], "[loadBillingData]", [5592, 5619], "[isAuthenticated, authUser, loadUserProfile]", [696, 713], "[generateReport, isOpen, project]", [3209, 3217], "[config, validateConfiguration]", [928, 938], "[overview, validateOverview]"]