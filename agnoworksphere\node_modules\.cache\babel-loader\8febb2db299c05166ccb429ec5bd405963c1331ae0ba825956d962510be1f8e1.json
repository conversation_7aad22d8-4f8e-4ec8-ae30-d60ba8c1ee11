{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\modals\\\\EnhancedCreateAIProjectModal.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Icon from '../AppIcon';\nimport ProjectExportButton from '../ui/ProjectExportButton';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst EnhancedCreateAIProjectModal = ({\n  isOpen,\n  onClose,\n  onCreateAIProject,\n  organizationId,\n  organizationName\n}) => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    projectType: 'general',\n    teamSize: 5,\n    teamExperience: 'intermediate'\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState(1);\n  const [projectPreview, setProjectPreview] = useState(null);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n  const projectTypes = [{\n    value: 'general',\n    label: 'General Project',\n    description: 'Standard project with flexible workflow',\n    icon: 'Folder',\n    color: 'bg-gray-500'\n  }, {\n    value: 'web_application',\n    label: 'Web Application',\n    description: 'Full-stack web development project',\n    icon: 'Globe',\n    color: 'bg-blue-500'\n  }, {\n    value: 'mobile_app',\n    label: 'Mobile App',\n    description: 'iOS/Android mobile application',\n    icon: 'Smartphone',\n    color: 'bg-green-500'\n  }, {\n    value: 'ecommerce_platform',\n    label: 'E-commerce Platform',\n    description: 'Online marketplace with payment processing',\n    icon: 'ShoppingCart',\n    color: 'bg-purple-500'\n  }, {\n    value: 'saas_application',\n    label: 'SaaS Application',\n    description: 'Multi-tenant cloud-based software',\n    icon: 'Cloud',\n    color: 'bg-indigo-500'\n  }, {\n    value: 'devops_infrastructure',\n    label: 'DevOps/Infrastructure',\n    description: 'CI/CD pipelines and infrastructure automation',\n    icon: 'Server',\n    color: 'bg-orange-500'\n  }];\n  const teamSizeOptions = [{\n    value: 2,\n    label: '2 people (Small team)'\n  }, {\n    value: 3,\n    label: '3 people (Small team)'\n  }, {\n    value: 5,\n    label: '5 people (Optimal team)'\n  }, {\n    value: 8,\n    label: '8 people (Large team)'\n  }, {\n    value: 12,\n    label: '12+ people (Enterprise team)'\n  }];\n  const experienceOptions = [{\n    value: 'junior',\n    label: 'Junior (0-2 years)'\n  }, {\n    value: 'intermediate',\n    label: 'Intermediate (2-5 years)'\n  }, {\n    value: 'senior',\n    label: 'Senior (5+ years)'\n  }, {\n    value: 'expert',\n    label: 'Expert (10+ years)'\n  }];\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    if (error) setError('');\n  };\n  const generatePreview = async () => {\n    if (!formData.name.trim()) {\n      setError('Project name is required for preview');\n      return;\n    }\n    setIsGeneratingPreview(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const selectedType = projectTypes.find(type => type.value === formData.projectType);\n      setProjectPreview({\n        name: formData.name,\n        type: selectedType,\n        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),\n        estimatedTasks: getEstimatedTaskCount(formData.projectType),\n        phases: getProjectPhases(formData.projectType),\n        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),\n        technologies: getTechnologies(formData.projectType)\n      });\n      setCurrentStep(2);\n    } catch (err) {\n      setError('Failed to generate preview');\n    } finally {\n      setIsGeneratingPreview(false);\n    }\n  };\n  const getEstimatedDuration = (type, teamSize, experience) => {\n    const baseDurations = {\n      'web_application': 50,\n      'mobile_app': 57,\n      'ecommerce_platform': 87,\n      'saas_application': 97,\n      'devops_infrastructure': 52,\n      'general': 43\n    };\n    let duration = baseDurations[type] || 43;\n    const experienceMultipliers = {\n      junior: 1.3,\n      intermediate: 1.0,\n      senior: 0.8,\n      expert: 0.7\n    };\n    duration *= experienceMultipliers[experience] || 1.0;\n    if (teamSize <= 2) duration *= 1.2;else if (teamSize >= 8) duration *= 0.9;\n    return Math.round(duration);\n  };\n  const getEstimatedTaskCount = type => {\n    const taskCounts = {\n      'web_application': 25,\n      'mobile_app': 28,\n      'ecommerce_platform': 45,\n      'saas_application': 52,\n      'devops_infrastructure': 22,\n      'general': 15\n    };\n    return taskCounts[type] || 15;\n  };\n  const getProjectPhases = type => {\n    const phases = {\n      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],\n      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch']\n    };\n    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];\n  };\n  const getTeamRecommendations = (size, experience) => {\n    const recommendations = [];\n    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');\n    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');\n    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');\n    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');\n    return recommendations;\n  };\n  const getTechnologies = type => {\n    const tech = {\n      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],\n      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker']\n    };\n    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (currentStep === 1) {\n      generatePreview();\n      return;\n    }\n    setIsLoading(true);\n    setError('');\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        project_type: formData.projectType,\n        team_size: formData.teamSize,\n        team_experience: formData.teamExperience,\n        organization_id: organizationId\n      };\n      await onCreateAIProject(projectData);\n      handleClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create AI project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleClose = () => {\n    if (!isLoading && !isGeneratingPreview) {\n      setFormData({\n        name: '',\n        projectType: 'general',\n        teamSize: 5,\n        teamExperience: 'intermediate'\n      });\n      setError('');\n      setCurrentStep(1);\n      setProjectPreview(null);\n      onClose();\n    }\n  };\n  const handleBack = () => {\n    setCurrentStep(1);\n    setProjectPreview(null);\n    setError('');\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-surface rounded-2xl shadow-enterprise border border-border w-full max-w-6xl max-h-[90vh] overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between p-8 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Sparkles\",\n              size: 24,\n              className: \"text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-foreground\",\n              children: \"Create AI Project\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-muted-foreground\",\n              children: \"Let AI generate your complete project structure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"icon\",\n          onClick: handleClose,\n          disabled: isLoading || isGeneratingPreview,\n          className: \"text-muted-foreground hover:text-foreground hover:bg-background/80 rounded-xl\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"X\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-8 py-6 bg-background/50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center space-x-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center transition-all duration-300 ${currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${currentStep >= 1 ? 'bg-primary text-primary-foreground shadow-lg scale-110' : 'bg-muted text-muted-foreground'}`,\n              children: \"1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 text-sm font-medium\",\n              children: \"Configure\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-20 h-1 rounded-full transition-all duration-300 ${currentStep >= 2 ? 'bg-primary' : 'bg-muted'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center transition-all duration-300 ${currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${currentStep >= 2 ? 'bg-primary text-primary-foreground shadow-lg scale-110' : 'bg-muted text-muted-foreground'}`,\n              children: \"2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"ml-3 text-sm font-medium\",\n              children: \"Preview & Create\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 overflow-y-auto max-h-[calc(90vh-200px)]\",\n        children: currentStep === 1 ? /*#__PURE__*/_jsxDEV(ConfigurationStep, {\n          formData: formData,\n          projectTypes: projectTypes,\n          teamSizeOptions: teamSizeOptions,\n          experienceOptions: experienceOptions,\n          onInputChange: handleInputChange,\n          onSubmit: handleSubmit,\n          isGeneratingPreview: isGeneratingPreview,\n          error: error,\n          organizationName: organizationName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(PreviewStep, {\n          projectPreview: projectPreview,\n          onSubmit: handleSubmit,\n          onBack: handleBack,\n          isLoading: isLoading,\n          error: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n\n// Configuration Step Component\n_s(EnhancedCreateAIProjectModal, \"5yHcF44uvLOBV3juB5sYYlNeEsw=\");\n_c = EnhancedCreateAIProjectModal;\nconst ConfigurationStep = ({\n  formData,\n  projectTypes,\n  teamSizeOptions,\n  experienceOptions,\n  onInputChange,\n  onSubmit,\n  isGeneratingPreview,\n  error,\n  organizationName\n}) => /*#__PURE__*/_jsxDEV(\"form\", {\n  onSubmit: onSubmit,\n  className: \"space-y-8\",\n  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\",\n        children: /*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Building2\",\n          size: 16,\n          className: \"text-white\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-muted-foreground\",\n          children: \"Creating in\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"font-semibold text-foreground\",\n          children: organizationName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 270,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 263,\n    columnNumber: 5\n  }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-destructive/10 border border-destructive/20 rounded-xl p-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center gap-3 text-destructive\",\n      children: [/*#__PURE__*/_jsxDEV(Icon, {\n        name: \"AlertCircle\",\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"font-medium\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 277,\n    columnNumber: 7\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          className: \"block text-sm font-semibold text-foreground\",\n          children: \"Project Name *\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          type: \"text\",\n          value: formData.name,\n          onChange: e => onInputChange('name', e.target.value),\n          placeholder: \"Enter your project name...\",\n          disabled: isGeneratingPreview,\n          className: \"w-full h-12 text-base\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card border border-border rounded-xl p-6 space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Users\",\n              size: 16,\n              className: \"text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-foreground\",\n            children: \"Team Configuration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-foreground\",\n              children: \"Team Size\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.teamSize,\n              onChange: value => onInputChange('teamSize', value),\n              options: teamSizeOptions,\n              disabled: isGeneratingPreview,\n              className: \"w-full h-12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-foreground\",\n              children: \"Team Experience Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: formData.teamExperience,\n              onChange: value => onInputChange('teamExperience', value),\n              options: experienceOptions,\n              disabled: isGeneratingPreview,\n              className: \"w-full h-12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Layers\",\n            size: 16,\n            className: \"text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-foreground\",\n          children: \"Project Type\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 gap-4 max-h-96 overflow-y-auto pr-2\",\n        children: projectTypes.map(type => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `group p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${formData.projectType === type.value ? 'border-primary bg-primary/5 shadow-md scale-[1.02]' : 'border-border hover:border-primary/50 hover:bg-primary/5'}`,\n          onClick: () => onInputChange('projectType', type.value),\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-start gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-12 h-12 ${type.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                name: type.icon,\n                size: 24,\n                className: \"text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 358,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-semibold text-foreground text-base\",\n                children: type.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-muted-foreground mt-1 leading-relaxed\",\n                children: type.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 17\n            }, this), formData.projectType === type.value && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0\",\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                name: \"Check\",\n                size: 14,\n                className: \"text-primary-foreground\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)\n        }, type.value, false, {\n          fileName: _jsxFileName,\n          lineNumber: 347,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 285,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex items-center justify-end gap-3 pt-4 border-t border-border\",\n    children: /*#__PURE__*/_jsxDEV(Button, {\n      type: \"submit\",\n      variant: \"default\",\n      disabled: isGeneratingPreview || !formData.name.trim(),\n      iconName: isGeneratingPreview ? \"Loader2\" : \"ArrowRight\",\n      iconPosition: \"right\",\n      className: `${isGeneratingPreview ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`,\n      children: isGeneratingPreview ? 'Generating Preview...' : 'Generate Preview'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 377,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 261,\n  columnNumber: 3\n}, this);\n\n// Preview Step Component\n_c2 = ConfigurationStep;\nconst PreviewStep = ({\n  projectPreview,\n  onSubmit,\n  onBack,\n  isLoading,\n  error\n}) => {\n  var _projectPreview$type, _projectPreview$type2, _projectPreview$type3, _projectPreview$phase, _projectPreview$techn, _projectPreview$phase2, _projectPreview$techn2, _projectPreview$teamR;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-red-50 border border-red-200 rounded-lg p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2 text-red-700\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"AlertCircle\",\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3 mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-12 h-12 ${(projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$type = projectPreview.type) === null || _projectPreview$type === void 0 ? void 0 : _projectPreview$type.color) || 'bg-gray-500'} rounded-lg flex items-center justify-center`,\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: (projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$type2 = projectPreview.type) === null || _projectPreview$type2 === void 0 ? void 0 : _projectPreview$type2.icon) || 'Folder',\n            size: 24,\n            className: \"text-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-purple-900\",\n            children: projectPreview === null || projectPreview === void 0 ? void 0 : projectPreview.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-purple-700\",\n            children: projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$type3 = projectPreview.type) === null || _projectPreview$type3 === void 0 ? void 0 : _projectPreview$type3.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-900\",\n            children: projectPreview === null || projectPreview === void 0 ? void 0 : projectPreview.estimatedDuration\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-purple-700\",\n            children: \"Days\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-900\",\n            children: projectPreview === null || projectPreview === void 0 ? void 0 : projectPreview.estimatedTasks\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-purple-700\",\n            children: \"Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-900\",\n            children: (projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$phase = projectPreview.phases) === null || _projectPreview$phase === void 0 ? void 0 : _projectPreview$phase.length) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-purple-700\",\n            children: \"Phases\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 426,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-2xl font-bold text-purple-900\",\n            children: (projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$techn = projectPreview.technologies) === null || _projectPreview$techn === void 0 ? void 0 : _projectPreview$techn.length) || 0\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-purple-700\",\n            children: \"Technologies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-text-primary flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"GitBranch\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 11\n          }, this), \"Project Phases\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-3\",\n          children: projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$phase2 = projectPreview.phases) === null || _projectPreview$phase2 === void 0 ? void 0 : _projectPreview$phase2.map((phase, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3 p-3 bg-muted rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium\",\n              children: index + 1\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium text-text-primary\",\n              children: phase\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 439,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-text-primary flex items-center gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Code\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 11\n          }, this), \"Recommended Technologies\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$techn2 = projectPreview.technologies) === null || _projectPreview$techn2 === void 0 ? void 0 : _projectPreview$techn2.map((tech, index) => /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\",\n            children: tech\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 462,\n          columnNumber: 9\n        }, this), (projectPreview === null || projectPreview === void 0 ? void 0 : (_projectPreview$teamR = projectPreview.teamRecommendations) === null || _projectPreview$teamR === void 0 ? void 0 : _projectPreview$teamR.length) > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-text-primary flex items-center gap-2 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Users\",\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this), \"Team Recommendations\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: projectPreview.teamRecommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start gap-2 text-sm text-text-secondary\",\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: \"CheckCircle\",\n                size: 16,\n                className: \"text-green-500 mt-0.5 flex-shrink-0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: rec\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 19\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 457,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-sm font-medium text-green-900 mb-2 flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Sparkles\",\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 493,\n          columnNumber: 9\n        }, this), \"What AI will generate for you:\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-green-800\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Comprehensive project description and objectives\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 497,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Detailed workflow with phases and milestones\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Complete task breakdown with priorities and estimates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Kanban board with organized task categories\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Project timeline and resource recommendations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"\\u2022 Risk assessment and mitigation strategies\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 496,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between pt-4 border-t border-border\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outline\",\n          onClick: onBack,\n          disabled: isLoading,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"ArrowLeft\",\n            size: 16,\n            className: \"mr-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 11\n          }, this), \"Back to Configuration\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(ProjectExportButton, {\n          projectData: projectPreview\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: onSubmit,\n        disabled: isLoading,\n        iconName: isLoading ? \"Loader2\" : \"Sparkles\",\n        iconPosition: \"left\",\n        className: `${isLoading ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`,\n        children: isLoading ? 'Creating Project...' : 'Create AI Project'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 514,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 506,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 3\n  }, this);\n};\n_c3 = PreviewStep;\nexport default EnhancedCreateAIProjectModal;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"EnhancedCreateAIProjectModal\");\n$RefreshReg$(_c2, \"ConfigurationStep\");\n$RefreshReg$(_c3, \"PreviewStep\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Select", "Icon", "ProjectExportButton", "jsxDEV", "_jsxDEV", "EnhancedCreateAIProjectModal", "isOpen", "onClose", "onCreateAIProject", "organizationId", "organizationName", "_s", "formData", "setFormData", "name", "projectType", "teamSize", "teamExperience", "isLoading", "setIsLoading", "error", "setError", "currentStep", "setCurrentStep", "projectPreview", "setProjectPreview", "isGeneratingPreview", "setIsGeneratingPreview", "projectTypes", "value", "label", "description", "icon", "color", "teamSizeOptions", "experienceOptions", "handleInputChange", "field", "prev", "generatePreview", "trim", "Promise", "resolve", "setTimeout", "selectedType", "find", "type", "estimatedDuration", "getEstimatedDuration", "estimatedTasks", "getEstimatedTaskCount", "phases", "getProjectPhases", "teamRecommendations", "getTeamRecommendations", "technologies", "getTechnologies", "err", "experience", "baseDurations", "duration", "experienceMultipliers", "junior", "intermediate", "senior", "expert", "Math", "round", "taskCounts", "size", "recommendations", "push", "tech", "handleSubmit", "e", "preventDefault", "projectData", "project_type", "team_size", "team_experience", "organization_id", "handleClose", "message", "handleBack", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "disabled", "ConfigurationStep", "onInputChange", "onSubmit", "PreviewStep", "onBack", "_c", "onChange", "target", "placeholder", "required", "options", "map", "iconName", "iconPosition", "_c2", "_projectPreview$type", "_projectPreview$type2", "_projectPreview$type3", "_projectPreview$phase", "_projectPreview$techn", "_projectPreview$phase2", "_projectPreview$techn2", "_projectPreview$teamR", "length", "phase", "index", "rec", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/modals/EnhancedCreateAIProjectModal.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport Button from '../ui/Button';\nimport Input from '../ui/Input';\nimport Select from '../ui/Select';\nimport Icon from '../AppIcon';\nimport ProjectExportButton from '../ui/ProjectExportButton';\n\nconst EnhancedCreateAIProjectModal = ({ isOpen, onClose, onCreateAIProject, organizationId, organizationName }) => {\n  const [formData, setFormData] = useState({\n    name: '',\n    projectType: 'general',\n    teamSize: 5,\n    teamExperience: 'intermediate'\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [currentStep, setCurrentStep] = useState(1);\n  const [projectPreview, setProjectPreview] = useState(null);\n  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);\n\n  const projectTypes = [\n    { value: 'general', label: 'General Project', description: 'Standard project with flexible workflow', icon: 'Folder', color: 'bg-gray-500' },\n    { value: 'web_application', label: 'Web Application', description: 'Full-stack web development project', icon: 'Globe', color: 'bg-blue-500' },\n    { value: 'mobile_app', label: 'Mobile App', description: 'iOS/Android mobile application', icon: 'Smartphone', color: 'bg-green-500' },\n    { value: 'ecommerce_platform', label: 'E-commerce Platform', description: 'Online marketplace with payment processing', icon: 'ShoppingCart', color: 'bg-purple-500' },\n    { value: 'saas_application', label: 'SaaS Application', description: 'Multi-tenant cloud-based software', icon: 'Cloud', color: 'bg-indigo-500' },\n    { value: 'devops_infrastructure', label: 'DevOps/Infrastructure', description: 'CI/CD pipelines and infrastructure automation', icon: 'Server', color: 'bg-orange-500' }\n  ];\n\n  const teamSizeOptions = [\n    { value: 2, label: '2 people (Small team)' },\n    { value: 3, label: '3 people (Small team)' },\n    { value: 5, label: '5 people (Optimal team)' },\n    { value: 8, label: '8 people (Large team)' },\n    { value: 12, label: '12+ people (Enterprise team)' }\n  ];\n\n  const experienceOptions = [\n    { value: 'junior', label: 'Junior (0-2 years)' },\n    { value: 'intermediate', label: 'Intermediate (2-5 years)' },\n    { value: 'senior', label: 'Senior (5+ years)' },\n    { value: 'expert', label: 'Expert (10+ years)' }\n  ];\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    if (error) setError('');\n  };\n\n  const generatePreview = async () => {\n    if (!formData.name.trim()) {\n      setError('Project name is required for preview');\n      return;\n    }\n\n    setIsGeneratingPreview(true);\n    try {\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      const selectedType = projectTypes.find(type => type.value === formData.projectType);\n      \n      setProjectPreview({\n        name: formData.name,\n        type: selectedType,\n        estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),\n        estimatedTasks: getEstimatedTaskCount(formData.projectType),\n        phases: getProjectPhases(formData.projectType),\n        teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),\n        technologies: getTechnologies(formData.projectType)\n      });\n      \n      setCurrentStep(2);\n    } catch (err) {\n      setError('Failed to generate preview');\n    } finally {\n      setIsGeneratingPreview(false);\n    }\n  };\n\n  const getEstimatedDuration = (type, teamSize, experience) => {\n    const baseDurations = {\n      'web_application': 50, 'mobile_app': 57, 'ecommerce_platform': 87,\n      'saas_application': 97, 'devops_infrastructure': 52, 'general': 43\n    };\n    \n    let duration = baseDurations[type] || 43;\n    const experienceMultipliers = { junior: 1.3, intermediate: 1.0, senior: 0.8, expert: 0.7 };\n    duration *= experienceMultipliers[experience] || 1.0;\n    \n    if (teamSize <= 2) duration *= 1.2;\n    else if (teamSize >= 8) duration *= 0.9;\n    \n    return Math.round(duration);\n  };\n\n  const getEstimatedTaskCount = (type) => {\n    const taskCounts = {\n      'web_application': 25, 'mobile_app': 28, 'ecommerce_platform': 45,\n      'saas_application': 52, 'devops_infrastructure': 22, 'general': 15\n    };\n    return taskCounts[type] || 15;\n  };\n\n  const getProjectPhases = (type) => {\n    const phases = {\n      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],\n      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch']\n    };\n    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];\n  };\n\n  const getTeamRecommendations = (size, experience) => {\n    const recommendations = [];\n    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');\n    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');\n    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');\n    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');\n    return recommendations;\n  };\n\n  const getTechnologies = (type) => {\n    const tech = {\n      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],\n      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker']\n    };\n    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (currentStep === 1) {\n      generatePreview();\n      return;\n    }\n\n    setIsLoading(true);\n    setError('');\n\n    try {\n      const projectData = {\n        name: formData.name.trim(),\n        project_type: formData.projectType,\n        team_size: formData.teamSize,\n        team_experience: formData.teamExperience,\n        organization_id: organizationId\n      };\n\n      await onCreateAIProject(projectData);\n      handleClose();\n    } catch (err) {\n      setError(err.message || 'Failed to create AI project');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleClose = () => {\n    if (!isLoading && !isGeneratingPreview) {\n      setFormData({ name: '', projectType: 'general', teamSize: 5, teamExperience: 'intermediate' });\n      setError('');\n      setCurrentStep(1);\n      setProjectPreview(null);\n      onClose();\n    }\n  };\n\n  const handleBack = () => {\n    setCurrentStep(1);\n    setProjectPreview(null);\n    setError('');\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-surface rounded-2xl shadow-enterprise border border-border w-full max-w-6xl max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-8 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg\">\n              <Icon name=\"Sparkles\" size={24} className=\"text-white\" />\n            </div>\n            <div>\n              <h2 className=\"text-2xl font-bold text-foreground\">Create AI Project</h2>\n              <p className=\"text-muted-foreground\">Let AI generate your complete project structure</p>\n            </div>\n          </div>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={handleClose}\n            disabled={isLoading || isGeneratingPreview}\n            className=\"text-muted-foreground hover:text-foreground hover:bg-background/80 rounded-xl\"\n          >\n            <Icon name=\"X\" size={20} />\n          </Button>\n        </div>\n\n        {/* Step Indicator */}\n        <div className=\"px-8 py-6 bg-background/50\">\n          <div className=\"flex items-center justify-center space-x-6\">\n            <div className={`flex items-center transition-all duration-300 ${currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${\n                currentStep >= 1\n                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'\n                  : 'bg-muted text-muted-foreground'\n              }`}>\n                1\n              </div>\n              <span className=\"ml-3 text-sm font-medium\">Configure</span>\n            </div>\n            <div className={`w-20 h-1 rounded-full transition-all duration-300 ${\n              currentStep >= 2 ? 'bg-primary' : 'bg-muted'\n            }`}></div>\n            <div className={`flex items-center transition-all duration-300 ${currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${\n                currentStep >= 2\n                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'\n                  : 'bg-muted text-muted-foreground'\n              }`}>\n                2\n              </div>\n              <span className=\"ml-3 text-sm font-medium\">Preview & Create</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-8 overflow-y-auto max-h-[calc(90vh-200px)]\">\n          {currentStep === 1 ? (\n            <ConfigurationStep\n              formData={formData}\n              projectTypes={projectTypes}\n              teamSizeOptions={teamSizeOptions}\n              experienceOptions={experienceOptions}\n              onInputChange={handleInputChange}\n              onSubmit={handleSubmit}\n              isGeneratingPreview={isGeneratingPreview}\n              error={error}\n              organizationName={organizationName}\n            />\n          ) : (\n            <PreviewStep\n              projectPreview={projectPreview}\n              onSubmit={handleSubmit}\n              onBack={handleBack}\n              isLoading={isLoading}\n              error={error}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Configuration Step Component\nconst ConfigurationStep = ({ formData, projectTypes, teamSizeOptions, experienceOptions, onInputChange, onSubmit, isGeneratingPreview, error, organizationName }) => (\n  <form onSubmit={onSubmit} className=\"space-y-8\">\n    {/* Organization Info */}\n    <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6\">\n      <div className=\"flex items-center gap-3\">\n        <div className=\"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center\">\n          <Icon name=\"Building2\" size={16} className=\"text-white\" />\n        </div>\n        <div>\n          <p className=\"text-sm text-muted-foreground\">Creating in</p>\n          <p className=\"font-semibold text-foreground\">{organizationName}</p>\n        </div>\n      </div>\n    </div>\n\n    {/* Error Message */}\n    {error && (\n      <div className=\"bg-destructive/10 border border-destructive/20 rounded-xl p-4\">\n        <div className=\"flex items-center gap-3 text-destructive\">\n          <Icon name=\"AlertCircle\" size={20} />\n          <span className=\"font-medium\">{error}</span>\n        </div>\n      </div>\n    )}\n\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n      {/* Left Column */}\n      <div className=\"space-y-8\">\n        {/* Project Name */}\n        <div className=\"space-y-3\">\n          <label className=\"block text-sm font-semibold text-foreground\">Project Name *</label>\n          <Input\n            type=\"text\"\n            value={formData.name}\n            onChange={(e) => onInputChange('name', e.target.value)}\n            placeholder=\"Enter your project name...\"\n            disabled={isGeneratingPreview}\n            className=\"w-full h-12 text-base\"\n            required\n          />\n        </div>\n\n        {/* Team Configuration */}\n        <div className=\"bg-card border border-border rounded-xl p-6 space-y-6\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center\">\n              <Icon name=\"Users\" size={16} className=\"text-white\" />\n            </div>\n            <h3 className=\"text-lg font-semibold text-foreground\">Team Configuration</h3>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm font-medium text-foreground\">Team Size</label>\n              <Select\n                value={formData.teamSize}\n                onChange={(value) => onInputChange('teamSize', value)}\n                options={teamSizeOptions}\n                disabled={isGeneratingPreview}\n                className=\"w-full h-12\"\n              />\n            </div>\n\n            <div className=\"space-y-3\">\n              <label className=\"block text-sm font-medium text-foreground\">Team Experience Level</label>\n              <Select\n                value={formData.teamExperience}\n                onChange={(value) => onInputChange('teamExperience', value)}\n                options={experienceOptions}\n                disabled={isGeneratingPreview}\n                className=\"w-full h-12\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Right Column - Project Type Selector */}\n      <div className=\"space-y-6\">\n        <div className=\"flex items-center gap-3\">\n          <div className=\"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center\">\n            <Icon name=\"Layers\" size={16} className=\"text-white\" />\n          </div>\n          <h3 className=\"text-lg font-semibold text-foreground\">Project Type</h3>\n        </div>\n        <div className=\"grid grid-cols-1 gap-4 max-h-96 overflow-y-auto pr-2\">\n          {projectTypes.map((type) => (\n            <div\n              key={type.value}\n              className={`group p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${\n                formData.projectType === type.value\n                  ? 'border-primary bg-primary/5 shadow-md scale-[1.02]'\n                  : 'border-border hover:border-primary/50 hover:bg-primary/5'\n              }`}\n              onClick={() => onInputChange('projectType', type.value)}\n            >\n              <div className=\"flex items-start gap-4\">\n                <div className={`w-12 h-12 ${type.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`}>\n                  <Icon name={type.icon} size={24} className=\"text-white\" />\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"font-semibold text-foreground text-base\">{type.label}</h4>\n                  <p className=\"text-sm text-muted-foreground mt-1 leading-relaxed\">{type.description}</p>\n                </div>\n                {formData.projectType === type.value && (\n                  <div className=\"w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0\">\n                    <Icon name=\"Check\" size={14} className=\"text-primary-foreground\" />\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n\n    {/* Actions */}\n    <div className=\"flex items-center justify-end gap-3 pt-4 border-t border-border\">\n      <Button\n        type=\"submit\"\n        variant=\"default\"\n        disabled={isGeneratingPreview || !formData.name.trim()}\n        iconName={isGeneratingPreview ? \"Loader2\" : \"ArrowRight\"}\n        iconPosition=\"right\"\n        className={`${isGeneratingPreview ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`}\n      >\n        {isGeneratingPreview ? 'Generating Preview...' : 'Generate Preview'}\n      </Button>\n    </div>\n  </form>\n);\n\n// Preview Step Component\nconst PreviewStep = ({ projectPreview, onSubmit, onBack, isLoading, error }) => (\n  <div className=\"space-y-6\">\n    {/* Error Message */}\n    {error && (\n      <div className=\"bg-red-50 border border-red-200 rounded-lg p-4\">\n        <div className=\"flex items-center gap-2 text-red-700\">\n          <Icon name=\"AlertCircle\" size={16} />\n          <span className=\"text-sm font-medium\">{error}</span>\n        </div>\n      </div>\n    )}\n\n    {/* Project Overview */}\n    <div className=\"bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6\">\n      <div className=\"flex items-center gap-3 mb-4\">\n        <div className={`w-12 h-12 ${projectPreview?.type?.color || 'bg-gray-500'} rounded-lg flex items-center justify-center`}>\n          <Icon name={projectPreview?.type?.icon || 'Folder'} size={24} className=\"text-white\" />\n        </div>\n        <div>\n          <h2 className=\"text-xl font-semibold text-purple-900\">{projectPreview?.name}</h2>\n          <p className=\"text-purple-700\">{projectPreview?.type?.label}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-purple-900\">{projectPreview?.estimatedDuration}</div>\n          <div className=\"text-sm text-purple-700\">Days</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-purple-900\">{projectPreview?.estimatedTasks}</div>\n          <div className=\"text-sm text-purple-700\">Tasks</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-purple-900\">{projectPreview?.phases?.length || 0}</div>\n          <div className=\"text-sm text-purple-700\">Phases</div>\n        </div>\n        <div className=\"text-center\">\n          <div className=\"text-2xl font-bold text-purple-900\">{projectPreview?.technologies?.length || 0}</div>\n          <div className=\"text-sm text-purple-700\">Technologies</div>\n        </div>\n      </div>\n    </div>\n\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      {/* Project Phases */}\n      <div className=\"space-y-4\">\n        <h3 className=\"text-lg font-medium text-text-primary flex items-center gap-2\">\n          <Icon name=\"GitBranch\" size={20} />\n          Project Phases\n        </h3>\n        <div className=\"space-y-3\">\n          {projectPreview?.phases?.map((phase, index) => (\n            <div key={index} className=\"flex items-center gap-3 p-3 bg-muted rounded-lg\">\n              <div className=\"w-8 h-8 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-medium\">\n                {index + 1}\n              </div>\n              <span className=\"font-medium text-text-primary\">{phase}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Technologies */}\n      <div className=\"space-y-4\">\n        <h3 className=\"text-lg font-medium text-text-primary flex items-center gap-2\">\n          <Icon name=\"Code\" size={20} />\n          Recommended Technologies\n        </h3>\n        <div className=\"flex flex-wrap gap-2\">\n          {projectPreview?.technologies?.map((tech, index) => (\n            <span key={index} className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\">\n              {tech}\n            </span>\n          ))}\n        </div>\n\n        {/* Team Recommendations */}\n        {projectPreview?.teamRecommendations?.length > 0 && (\n          <div className=\"mt-6\">\n            <h4 className=\"text-md font-medium text-text-primary flex items-center gap-2 mb-3\">\n              <Icon name=\"Users\" size={18} />\n              Team Recommendations\n            </h4>\n            <div className=\"space-y-2\">\n              {projectPreview.teamRecommendations.map((rec, index) => (\n                <div key={index} className=\"flex items-start gap-2 text-sm text-text-secondary\">\n                  <Icon name=\"CheckCircle\" size={16} className=\"text-green-500 mt-0.5 flex-shrink-0\" />\n                  <span>{rec}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n\n    {/* AI Features Info */}\n    <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4\">\n      <h3 className=\"text-sm font-medium text-green-900 mb-2 flex items-center gap-2\">\n        <Icon name=\"Sparkles\" size={16} />\n        What AI will generate for you:\n      </h3>\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-green-800\">\n        <div>• Comprehensive project description and objectives</div>\n        <div>• Detailed workflow with phases and milestones</div>\n        <div>• Complete task breakdown with priorities and estimates</div>\n        <div>• Kanban board with organized task categories</div>\n        <div>• Project timeline and resource recommendations</div>\n        <div>• Risk assessment and mitigation strategies</div>\n      </div>\n    </div>\n\n    <div className=\"flex items-center justify-between pt-4 border-t border-border\">\n      <div className=\"flex items-center gap-3\">\n        <Button variant=\"outline\" onClick={onBack} disabled={isLoading}>\n          <Icon name=\"ArrowLeft\" size={16} className=\"mr-2\" />\n          Back to Configuration\n        </Button>\n        <ProjectExportButton projectData={projectPreview} />\n      </div>\n      <Button\n        onClick={onSubmit}\n        disabled={isLoading}\n        iconName={isLoading ? \"Loader2\" : \"Sparkles\"}\n        iconPosition=\"left\"\n        className={`${isLoading ? \"animate-spin\" : \"\"} bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 border-0`}\n      >\n        {isLoading ? 'Creating Project...' : 'Create AI Project'}\n      </Button>\n    </div>\n  </div>\n);\n\nexport default EnhancedCreateAIProjectModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;AAC/B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,mBAAmB,MAAM,2BAA2B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,4BAA4B,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAAEC,iBAAiB;EAAEC,cAAc;EAAEC;AAAiB,CAAC,KAAK;EAAAC,EAAA;EACjH,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC;IACvCkB,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,SAAS;IACtBC,QAAQ,EAAE,CAAC;IACXC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,WAAW,EAAEC,cAAc,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAAC8B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAErE,MAAMgC,YAAY,GAAG,CACnB;IAAEC,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,yCAAyC;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC5I;IAAEJ,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,iBAAiB;IAAEC,WAAW,EAAE,oCAAoC;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9I;IAAEJ,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,WAAW,EAAE,gCAAgC;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAe,CAAC,EACtI;IAAEJ,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,WAAW,EAAE,4CAA4C;IAAEC,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACtK;IAAEJ,KAAK,EAAE,kBAAkB;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,WAAW,EAAE,mCAAmC;IAAEC,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAgB,CAAC,EACjJ;IAAEJ,KAAK,EAAE,uBAAuB;IAAEC,KAAK,EAAE,uBAAuB;IAAEC,WAAW,EAAE,+CAA+C;IAAEC,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAgB,CAAC,CACzK;EAED,MAAMC,eAAe,GAAG,CACtB;IAAEL,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAA0B,CAAC,EAC9C;IAAED,KAAK,EAAE,CAAC;IAAEC,KAAK,EAAE;EAAwB,CAAC,EAC5C;IAAED,KAAK,EAAE,EAAE;IAAEC,KAAK,EAAE;EAA+B,CAAC,CACrD;EAED,MAAMK,iBAAiB,GAAG,CACxB;IAAEN,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAqB,CAAC,EAChD;IAAED,KAAK,EAAE,cAAc;IAAEC,KAAK,EAAE;EAA2B,CAAC,EAC5D;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC/C;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAqB,CAAC,CACjD;EAED,MAAMM,iBAAiB,GAAGA,CAACC,KAAK,EAAER,KAAK,KAAK;IAC1ChB,WAAW,CAACyB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,KAAK,GAAGR;IAAM,CAAC,CAAC,CAAC;IAClD,IAAIT,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMkB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAAC3B,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC,EAAE;MACzBnB,QAAQ,CAAC,sCAAsC,CAAC;MAChD;IACF;IAEAM,sBAAsB,CAAC,IAAI,CAAC;IAC5B,IAAI;MACF,MAAM,IAAIc,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,YAAY,GAAGhB,YAAY,CAACiB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACjB,KAAK,KAAKjB,QAAQ,CAACG,WAAW,CAAC;MAEnFU,iBAAiB,CAAC;QAChBX,IAAI,EAAEF,QAAQ,CAACE,IAAI;QACnBgC,IAAI,EAAEF,YAAY;QAClBG,iBAAiB,EAAEC,oBAAoB,CAACpC,QAAQ,CAACG,WAAW,EAAEH,QAAQ,CAACI,QAAQ,EAAEJ,QAAQ,CAACK,cAAc,CAAC;QACzGgC,cAAc,EAAEC,qBAAqB,CAACtC,QAAQ,CAACG,WAAW,CAAC;QAC3DoC,MAAM,EAAEC,gBAAgB,CAACxC,QAAQ,CAACG,WAAW,CAAC;QAC9CsC,mBAAmB,EAAEC,sBAAsB,CAAC1C,QAAQ,CAACI,QAAQ,EAAEJ,QAAQ,CAACK,cAAc,CAAC;QACvFsC,YAAY,EAAEC,eAAe,CAAC5C,QAAQ,CAACG,WAAW;MACpD,CAAC,CAAC;MAEFQ,cAAc,CAAC,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkC,GAAG,EAAE;MACZpC,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,SAAS;MACRM,sBAAsB,CAAC,KAAK,CAAC;IAC/B;EACF,CAAC;EAED,MAAMqB,oBAAoB,GAAGA,CAACF,IAAI,EAAE9B,QAAQ,EAAE0C,UAAU,KAAK;IAC3D,MAAMC,aAAa,GAAG;MACpB,iBAAiB,EAAE,EAAE;MAAE,YAAY,EAAE,EAAE;MAAE,oBAAoB,EAAE,EAAE;MACjE,kBAAkB,EAAE,EAAE;MAAE,uBAAuB,EAAE,EAAE;MAAE,SAAS,EAAE;IAClE,CAAC;IAED,IAAIC,QAAQ,GAAGD,aAAa,CAACb,IAAI,CAAC,IAAI,EAAE;IACxC,MAAMe,qBAAqB,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,YAAY,EAAE,GAAG;MAAEC,MAAM,EAAE,GAAG;MAAEC,MAAM,EAAE;IAAI,CAAC;IAC1FL,QAAQ,IAAIC,qBAAqB,CAACH,UAAU,CAAC,IAAI,GAAG;IAEpD,IAAI1C,QAAQ,IAAI,CAAC,EAAE4C,QAAQ,IAAI,GAAG,CAAC,KAC9B,IAAI5C,QAAQ,IAAI,CAAC,EAAE4C,QAAQ,IAAI,GAAG;IAEvC,OAAOM,IAAI,CAACC,KAAK,CAACP,QAAQ,CAAC;EAC7B,CAAC;EAED,MAAMV,qBAAqB,GAAIJ,IAAI,IAAK;IACtC,MAAMsB,UAAU,GAAG;MACjB,iBAAiB,EAAE,EAAE;MAAE,YAAY,EAAE,EAAE;MAAE,oBAAoB,EAAE,EAAE;MACjE,kBAAkB,EAAE,EAAE;MAAE,uBAAuB,EAAE,EAAE;MAAE,SAAS,EAAE;IAClE,CAAC;IACD,OAAOA,UAAU,CAACtB,IAAI,CAAC,IAAI,EAAE;EAC/B,CAAC;EAED,MAAMM,gBAAgB,GAAIN,IAAI,IAAK;IACjC,MAAMK,MAAM,GAAG;MACb,iBAAiB,EAAE,CAAC,qBAAqB,EAAE,uBAAuB,EAAE,aAAa,EAAE,cAAc,EAAE,qBAAqB,CAAC;MACzH,oBAAoB,EAAE,CAAC,4BAA4B,EAAE,2BAA2B,EAAE,gCAAgC,EAAE,kCAAkC,EAAE,kBAAkB;IAC5K,CAAC;IACD,OAAOA,MAAM,CAACL,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,uBAAuB,EAAE,oBAAoB,CAAC;EAC3I,CAAC;EAED,MAAMQ,sBAAsB,GAAGA,CAACe,IAAI,EAAEX,UAAU,KAAK;IACnD,MAAMY,eAAe,GAAG,EAAE;IAC1B,IAAID,IAAI,IAAI,CAAC,EAAEC,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC;IAC3F,IAAIF,IAAI,IAAI,CAAC,EAAEC,eAAe,CAACC,IAAI,CAAC,8DAA8D,CAAC;IACnG,IAAIb,UAAU,KAAK,QAAQ,EAAEY,eAAe,CAACC,IAAI,CAAC,mCAAmC,CAAC;IACtF,IAAIb,UAAU,KAAK,QAAQ,EAAEY,eAAe,CAACC,IAAI,CAAC,sDAAsD,CAAC;IACzG,OAAOD,eAAe;EACxB,CAAC;EAED,MAAMd,eAAe,GAAIV,IAAI,IAAK;IAChC,MAAM0B,IAAI,GAAG;MACX,iBAAiB,EAAE,CAAC,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,QAAQ,EAAE,WAAW,CAAC;MAC1F,oBAAoB,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,YAAY,EAAE,eAAe,EAAE,OAAO,EAAE,QAAQ;IAC7G,CAAC;IACD,OAAOA,IAAI,CAAC1B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,UAAU,EAAE,gBAAgB,CAAC;EAC/F,CAAC;EAED,MAAM2B,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAIrD,WAAW,KAAK,CAAC,EAAE;MACrBiB,eAAe,CAAC,CAAC;MACjB;IACF;IAEApB,YAAY,CAAC,IAAI,CAAC;IAClBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMuD,WAAW,GAAG;QAClB9D,IAAI,EAAEF,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAC;QAC1BqC,YAAY,EAAEjE,QAAQ,CAACG,WAAW;QAClC+D,SAAS,EAAElE,QAAQ,CAACI,QAAQ;QAC5B+D,eAAe,EAAEnE,QAAQ,CAACK,cAAc;QACxC+D,eAAe,EAAEvE;MACnB,CAAC;MAED,MAAMD,iBAAiB,CAACoE,WAAW,CAAC;MACpCK,WAAW,CAAC,CAAC;IACf,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACZpC,QAAQ,CAACoC,GAAG,CAACyB,OAAO,IAAI,6BAA6B,CAAC;IACxD,CAAC,SAAS;MACR/D,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM8D,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAAC/D,SAAS,IAAI,CAACQ,mBAAmB,EAAE;MACtCb,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,WAAW,EAAE,SAAS;QAAEC,QAAQ,EAAE,CAAC;QAAEC,cAAc,EAAE;MAAe,CAAC,CAAC;MAC9FI,QAAQ,CAAC,EAAE,CAAC;MACZE,cAAc,CAAC,CAAC,CAAC;MACjBE,iBAAiB,CAAC,IAAI,CAAC;MACvBlB,OAAO,CAAC,CAAC;IACX;EACF,CAAC;EAED,MAAM4E,UAAU,GAAGA,CAAA,KAAM;IACvB5D,cAAc,CAAC,CAAC,CAAC;IACjBE,iBAAiB,CAAC,IAAI,CAAC;IACvBJ,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,IAAI,CAACf,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEF,OAAA;IAAKgF,SAAS,EAAC,sFAAsF;IAAAC,QAAA,eACnGjF,OAAA;MAAKgF,SAAS,EAAC,6GAA6G;MAAAC,QAAA,gBAE1HjF,OAAA;QAAKgF,SAAS,EAAC,yGAAyG;QAAAC,QAAA,gBACtHjF,OAAA;UAAKgF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCjF,OAAA;YAAKgF,SAAS,EAAC,8HAA8H;YAAAC,QAAA,eAC3IjF,OAAA,CAACH,IAAI;cAACa,IAAI,EAAC,UAAU;cAACuD,IAAI,EAAE,EAAG;cAACe,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNrF,OAAA;YAAAiF,QAAA,gBACEjF,OAAA;cAAIgF,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzErF,OAAA;cAAGgF,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA+C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNrF,OAAA,CAACN,MAAM;UACL4F,OAAO,EAAC,OAAO;UACfrB,IAAI,EAAC,MAAM;UACXsB,OAAO,EAAEV,WAAY;UACrBW,QAAQ,EAAE1E,SAAS,IAAIQ,mBAAoB;UAC3C0D,SAAS,EAAC,+EAA+E;UAAAC,QAAA,eAEzFjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAC,GAAG;YAACuD,IAAI,EAAE;UAAG;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCjF,OAAA;UAAKgF,SAAS,EAAC,4CAA4C;UAAAC,QAAA,gBACzDjF,OAAA;YAAKgF,SAAS,EAAE,iDAAiD9D,WAAW,IAAI,CAAC,GAAG,cAAc,GAAG,uBAAuB,EAAG;YAAA+D,QAAA,gBAC7HjF,OAAA;cAAKgF,SAAS,EAAE,qGACd9D,WAAW,IAAI,CAAC,GACZ,wDAAwD,GACxD,gCAAgC,EACnC;cAAA+D,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrF,OAAA;cAAMgF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNrF,OAAA;YAAKgF,SAAS,EAAE,qDACd9D,WAAW,IAAI,CAAC,GAAG,YAAY,GAAG,UAAU;UAC3C;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACVrF,OAAA;YAAKgF,SAAS,EAAE,iDAAiD9D,WAAW,IAAI,CAAC,GAAG,cAAc,GAAG,uBAAuB,EAAG;YAAA+D,QAAA,gBAC7HjF,OAAA;cAAKgF,SAAS,EAAE,qGACd9D,WAAW,IAAI,CAAC,GACZ,wDAAwD,GACxD,gCAAgC,EACnC;cAAA+D,QAAA,EAAC;YAEJ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNrF,OAAA;cAAMgF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,8CAA8C;QAAAC,QAAA,EAC1D/D,WAAW,KAAK,CAAC,gBAChBlB,OAAA,CAACyF,iBAAiB;UAChBjF,QAAQ,EAAEA,QAAS;UACnBgB,YAAY,EAAEA,YAAa;UAC3BM,eAAe,EAAEA,eAAgB;UACjCC,iBAAiB,EAAEA,iBAAkB;UACrC2D,aAAa,EAAE1D,iBAAkB;UACjC2D,QAAQ,EAAEtB,YAAa;UACvB/C,mBAAmB,EAAEA,mBAAoB;UACzCN,KAAK,EAAEA,KAAM;UACbV,gBAAgB,EAAEA;QAAiB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,gBAEFrF,OAAA,CAAC4F,WAAW;UACVxE,cAAc,EAAEA,cAAe;UAC/BuE,QAAQ,EAAEtB,YAAa;UACvBwB,MAAM,EAAEd,UAAW;UACnBjE,SAAS,EAAEA,SAAU;UACrBE,KAAK,EAAEA;QAAM;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA9E,EAAA,CA3PMN,4BAA4B;AAAA6F,EAAA,GAA5B7F,4BAA4B;AA4PlC,MAAMwF,iBAAiB,GAAGA,CAAC;EAAEjF,QAAQ;EAAEgB,YAAY;EAAEM,eAAe;EAAEC,iBAAiB;EAAE2D,aAAa;EAAEC,QAAQ;EAAErE,mBAAmB;EAAEN,KAAK;EAAEV;AAAiB,CAAC,kBAC9JN,OAAA;EAAM2F,QAAQ,EAAEA,QAAS;EAACX,SAAS,EAAC,WAAW;EAAAC,QAAA,gBAE7CjF,OAAA;IAAKgF,SAAS,EAAC,kFAAkF;IAAAC,QAAA,eAC/FjF,OAAA;MAAKgF,SAAS,EAAC,yBAAyB;MAAAC,QAAA,gBACtCjF,OAAA;QAAKgF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,eAC9EjF,OAAA,CAACH,IAAI;UAACa,IAAI,EAAC,WAAW;UAACuD,IAAI,EAAE,EAAG;UAACe,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACNrF,OAAA;QAAAiF,QAAA,gBACEjF,OAAA;UAAGgF,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5DrF,OAAA;UAAGgF,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAAE3E;QAAgB;UAAA4E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,EAGLrE,KAAK,iBACJhB,OAAA;IAAKgF,SAAS,EAAC,+DAA+D;IAAAC,QAAA,eAC5EjF,OAAA;MAAKgF,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACvDjF,OAAA,CAACH,IAAI;QAACa,IAAI,EAAC,aAAa;QAACuD,IAAI,EAAE;MAAG;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrCrF,OAAA;QAAMgF,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAEjE;MAAK;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN,eAEDrF,OAAA;IAAKgF,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAEpDjF,OAAA;MAAKgF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBAExBjF,OAAA;QAAKgF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjF,OAAA;UAAOgF,SAAS,EAAC,6CAA6C;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrFrF,OAAA,CAACL,KAAK;UACJ+C,IAAI,EAAC,MAAM;UACXjB,KAAK,EAAEjB,QAAQ,CAACE,IAAK;UACrBqF,QAAQ,EAAGzB,CAAC,IAAKoB,aAAa,CAAC,MAAM,EAAEpB,CAAC,CAAC0B,MAAM,CAACvE,KAAK,CAAE;UACvDwE,WAAW,EAAC,4BAA4B;UACxCT,QAAQ,EAAElE,mBAAoB;UAC9B0D,SAAS,EAAC,uBAAuB;UACjCkB,QAAQ;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,uDAAuD;QAAAC,QAAA,gBACpEjF,OAAA;UAAKgF,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCjF,OAAA;YAAKgF,SAAS,EAAC,kEAAkE;YAAAC,QAAA,eAC/EjF,OAAA,CAACH,IAAI;cAACa,IAAI,EAAC,OAAO;cAACuD,IAAI,EAAE,EAAG;cAACe,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNrF,OAAA;YAAIgF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAENrF,OAAA;UAAKgF,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBjF,OAAA;YAAKgF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjF,OAAA;cAAOgF,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9ErF,OAAA,CAACJ,MAAM;cACL6B,KAAK,EAAEjB,QAAQ,CAACI,QAAS;cACzBmF,QAAQ,EAAGtE,KAAK,IAAKiE,aAAa,CAAC,UAAU,EAAEjE,KAAK,CAAE;cACtD0E,OAAO,EAAErE,eAAgB;cACzB0D,QAAQ,EAAElE,mBAAoB;cAC9B0D,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENrF,OAAA;YAAKgF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjF,OAAA;cAAOgF,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1FrF,OAAA,CAACJ,MAAM;cACL6B,KAAK,EAAEjB,QAAQ,CAACK,cAAe;cAC/BkF,QAAQ,EAAGtE,KAAK,IAAKiE,aAAa,CAAC,gBAAgB,EAAEjE,KAAK,CAAE;cAC5D0E,OAAO,EAAEpE,iBAAkB;cAC3ByD,QAAQ,EAAElE,mBAAoB;cAC9B0D,SAAS,EAAC;YAAa;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAKgF,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBjF,OAAA;QAAKgF,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCjF,OAAA;UAAKgF,SAAS,EAAC,mEAAmE;UAAAC,QAAA,eAChFjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAC,QAAQ;YAACuD,IAAI,EAAE,EAAG;YAACe,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNrF,OAAA;UAAIgF,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpE,CAAC,eACNrF,OAAA;QAAKgF,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEzD,YAAY,CAAC4E,GAAG,CAAE1D,IAAI,iBACrB1C,OAAA;UAEEgF,SAAS,EAAE,4FACTxE,QAAQ,CAACG,WAAW,KAAK+B,IAAI,CAACjB,KAAK,GAC/B,oDAAoD,GACpD,0DAA0D,EAC7D;UACH8D,OAAO,EAAEA,CAAA,KAAMG,aAAa,CAAC,aAAa,EAAEhD,IAAI,CAACjB,KAAK,CAAE;UAAAwD,QAAA,eAExDjF,OAAA;YAAKgF,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrCjF,OAAA;cAAKgF,SAAS,EAAE,aAAatC,IAAI,CAACb,KAAK,8GAA+G;cAAAoD,QAAA,eACpJjF,OAAA,CAACH,IAAI;gBAACa,IAAI,EAAEgC,IAAI,CAACd,IAAK;gBAACqC,IAAI,EAAE,EAAG;gBAACe,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eACNrF,OAAA;cAAKgF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BjF,OAAA;gBAAIgF,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EAAEvC,IAAI,CAAChB;cAAK;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACzErF,OAAA;gBAAGgF,SAAS,EAAC,oDAAoD;gBAAAC,QAAA,EAAEvC,IAAI,CAACf;cAAW;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,EACL7E,QAAQ,CAACG,WAAW,KAAK+B,IAAI,CAACjB,KAAK,iBAClCzB,OAAA;cAAKgF,SAAS,EAAC,gFAAgF;cAAAC,QAAA,eAC7FjF,OAAA,CAACH,IAAI;gBAACa,IAAI,EAAC,OAAO;gBAACuD,IAAI,EAAE,EAAG;gBAACe,SAAS,EAAC;cAAyB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC,GArBD3C,IAAI,CAACjB,KAAK;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAsBZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC,eAGNrF,OAAA;IAAKgF,SAAS,EAAC,iEAAiE;IAAAC,QAAA,eAC9EjF,OAAA,CAACN,MAAM;MACLgD,IAAI,EAAC,QAAQ;MACb4C,OAAO,EAAC,SAAS;MACjBE,QAAQ,EAAElE,mBAAmB,IAAI,CAACd,QAAQ,CAACE,IAAI,CAAC0B,IAAI,CAAC,CAAE;MACvDiE,QAAQ,EAAE/E,mBAAmB,GAAG,SAAS,GAAG,YAAa;MACzDgF,YAAY,EAAC,OAAO;MACpBtB,SAAS,EAAE,GAAG1D,mBAAmB,GAAG,cAAc,GAAG,EAAE,gGAAiG;MAAA2D,QAAA,EAEvJ3D,mBAAmB,GAAG,uBAAuB,GAAG;IAAkB;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACF,CACP;;AAED;AAAAkB,GAAA,GApIMd,iBAAiB;AAqIvB,MAAMG,WAAW,GAAGA,CAAC;EAAExE,cAAc;EAAEuE,QAAQ;EAAEE,MAAM;EAAE/E,SAAS;EAAEE;AAAM,CAAC;EAAA,IAAAwF,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;EAAA,oBACzE/G,OAAA;IAAKgF,SAAS,EAAC,WAAW;IAAAC,QAAA,GAEvBjE,KAAK,iBACJhB,OAAA;MAAKgF,SAAS,EAAC,gDAAgD;MAAAC,QAAA,eAC7DjF,OAAA;QAAKgF,SAAS,EAAC,sCAAsC;QAAAC,QAAA,gBACnDjF,OAAA,CAACH,IAAI;UAACa,IAAI,EAAC,aAAa;UAACuD,IAAI,EAAE;QAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrCrF,OAAA;UAAMgF,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAEjE;QAAK;UAAAkE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDrF,OAAA;MAAKgF,SAAS,EAAC,oFAAoF;MAAAC,QAAA,gBACjGjF,OAAA;QAAKgF,SAAS,EAAC,8BAA8B;QAAAC,QAAA,gBAC3CjF,OAAA;UAAKgF,SAAS,EAAE,aAAa,CAAA5D,cAAc,aAAdA,cAAc,wBAAAoF,oBAAA,GAAdpF,cAAc,CAAEsB,IAAI,cAAA8D,oBAAA,uBAApBA,oBAAA,CAAsB3E,KAAK,KAAI,aAAa,8CAA+C;UAAAoD,QAAA,eACtHjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAE,CAAAU,cAAc,aAAdA,cAAc,wBAAAqF,qBAAA,GAAdrF,cAAc,CAAEsB,IAAI,cAAA+D,qBAAA,uBAApBA,qBAAA,CAAsB7E,IAAI,KAAI,QAAS;YAACqC,IAAI,EAAE,EAAG;YAACe,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpF,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACEjF,OAAA;YAAIgF,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAE7D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEV;UAAI;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACjFrF,OAAA;YAAGgF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAE7D,cAAc,aAAdA,cAAc,wBAAAsF,qBAAA,GAAdtF,cAAc,CAAEsB,IAAI,cAAAgE,qBAAA,uBAApBA,qBAAA,CAAsBhF;UAAK;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENrF,OAAA;QAAKgF,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDjF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAKgF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAE7D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEuB;UAAiB;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7FrF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAKgF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAE7D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyB;UAAc;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1FrF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAKgF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAE,CAAA7D,cAAc,aAAdA,cAAc,wBAAAuF,qBAAA,GAAdvF,cAAc,CAAE2B,MAAM,cAAA4D,qBAAA,uBAAtBA,qBAAA,CAAwBK,MAAM,KAAI;UAAC;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC/FrF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BjF,OAAA;YAAKgF,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAE,CAAA7D,cAAc,aAAdA,cAAc,wBAAAwF,qBAAA,GAAdxF,cAAc,CAAE+B,YAAY,cAAAyD,qBAAA,uBAA5BA,qBAAA,CAA8BI,MAAM,KAAI;UAAC;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrGrF,OAAA;YAAKgF,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrF,OAAA;MAAKgF,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBAEpDjF,OAAA;QAAKgF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjF,OAAA;UAAIgF,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC3EjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAC,WAAW;YAACuD,IAAI,EAAE;UAAG;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,kBAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrF,OAAA;UAAKgF,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB7D,cAAc,aAAdA,cAAc,wBAAAyF,sBAAA,GAAdzF,cAAc,CAAE2B,MAAM,cAAA8D,sBAAA,uBAAtBA,sBAAA,CAAwBT,GAAG,CAAC,CAACa,KAAK,EAAEC,KAAK,kBACxClH,OAAA;YAAiBgF,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC1EjF,OAAA;cAAKgF,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAChHiC,KAAK,GAAG;YAAC;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,eACNrF,OAAA;cAAMgF,SAAS,EAAC,+BAA+B;cAAAC,QAAA,EAAEgC;YAAK;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAJtD6B,KAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKV,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBjF,OAAA;UAAIgF,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC3EjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAC,MAAM;YAACuD,IAAI,EAAE;UAAG;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,4BAEhC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLrF,OAAA;UAAKgF,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClC7D,cAAc,aAAdA,cAAc,wBAAA0F,sBAAA,GAAd1F,cAAc,CAAE+B,YAAY,cAAA2D,sBAAA,uBAA5BA,sBAAA,CAA8BV,GAAG,CAAC,CAAChC,IAAI,EAAE8C,KAAK,kBAC7ClH,OAAA;YAAkBgF,SAAS,EAAC,sEAAsE;YAAAC,QAAA,EAC/Fb;UAAI,GADI8C,KAAK;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGL,CAAAjE,cAAc,aAAdA,cAAc,wBAAA2F,qBAAA,GAAd3F,cAAc,CAAE6B,mBAAmB,cAAA8D,qBAAA,uBAAnCA,qBAAA,CAAqCC,MAAM,IAAG,CAAC,iBAC9ChH,OAAA;UAAKgF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBjF,OAAA;YAAIgF,SAAS,EAAC,oEAAoE;YAAAC,QAAA,gBAChFjF,OAAA,CAACH,IAAI;cAACa,IAAI,EAAC,OAAO;cAACuD,IAAI,EAAE;YAAG;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,wBAEjC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrF,OAAA;YAAKgF,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvB7D,cAAc,CAAC6B,mBAAmB,CAACmD,GAAG,CAAC,CAACe,GAAG,EAAED,KAAK,kBACjDlH,OAAA;cAAiBgF,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBAC7EjF,OAAA,CAACH,IAAI;gBAACa,IAAI,EAAC,aAAa;gBAACuD,IAAI,EAAE,EAAG;gBAACe,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACrFrF,OAAA;gBAAAiF,QAAA,EAAOkC;cAAG;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAFV6B,KAAK;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAKgF,SAAS,EAAC,qFAAqF;MAAAC,QAAA,gBAClGjF,OAAA;QAAIgF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,gBAC7EjF,OAAA,CAACH,IAAI;UAACa,IAAI,EAAC,UAAU;UAACuD,IAAI,EAAE;QAAG;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,kCAEpC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLrF,OAAA;QAAKgF,SAAS,EAAC,8DAA8D;QAAAC,QAAA,gBAC3EjF,OAAA;UAAAiF,QAAA,EAAK;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7DrF,OAAA;UAAAiF,QAAA,EAAK;QAA8C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACzDrF,OAAA;UAAAiF,QAAA,EAAK;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClErF,OAAA;UAAAiF,QAAA,EAAK;QAA6C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxDrF,OAAA;UAAAiF,QAAA,EAAK;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC1DrF,OAAA;UAAAiF,QAAA,EAAK;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENrF,OAAA;MAAKgF,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAC5EjF,OAAA;QAAKgF,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCjF,OAAA,CAACN,MAAM;UAAC4F,OAAO,EAAC,SAAS;UAACC,OAAO,EAAEM,MAAO;UAACL,QAAQ,EAAE1E,SAAU;UAAAmE,QAAA,gBAC7DjF,OAAA,CAACH,IAAI;YAACa,IAAI,EAAC,WAAW;YAACuD,IAAI,EAAE,EAAG;YAACe,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,yBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTrF,OAAA,CAACF,mBAAmB;UAAC0E,WAAW,EAAEpD;QAAe;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eACNrF,OAAA,CAACN,MAAM;QACL6F,OAAO,EAAEI,QAAS;QAClBH,QAAQ,EAAE1E,SAAU;QACpBuF,QAAQ,EAAEvF,SAAS,GAAG,SAAS,GAAG,UAAW;QAC7CwF,YAAY,EAAC,MAAM;QACnBtB,SAAS,EAAE,GAAGlE,SAAS,GAAG,cAAc,GAAG,EAAE,gGAAiG;QAAAmE,QAAA,EAE7InE,SAAS,GAAG,qBAAqB,GAAG;MAAmB;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAAA,CACP;AAAC+B,GAAA,GApIIxB,WAAW;AAsIjB,eAAe3F,4BAA4B;AAAC,IAAA6F,EAAA,EAAAS,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAvB,EAAA;AAAAuB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}